<!--主题设计器页面-->
<view class="designer-page">
  <!-- 顶部工具栏 -->
  <view class="top-toolbar" wx:if="{{showToolbar && !previewMode}}">
    <view class="toolbar-left">
      <view class="tool-btn" bindtap="onPreview">
        <van-icon name="eye-o" size="18px" />
        <text>预览</text>
      </view>
    </view>
    
    <view class="toolbar-center">
      <input 
        class="theme-name-input"
        value="{{theme.name}}"
        placeholder="主题名称"
        bindinput="onThemeNameChange"
      />
    </view>
    
    <view class="toolbar-right">
      <view class="tool-btn" bindtap="saveTheme">
        <van-icon name="completed" size="18px" />
        <text>保存</text>
      </view>
      <view class="tool-btn" bindtap="onPublishTheme">
        <van-icon name="share" size="18px" />
        <text>发布</text>
      </view>
    </view>
  </view>

  <!-- 设计画布 -->
  <view class="design-canvas">
    <!-- 设备预览框 -->
    <view class="device-frame">
      <view class="screen-container">
        <!-- 背景 -->
        <view 
          class="canvas-background"
          style="{{getBackgroundStyle(theme.config.background)}}"
        ></view>
        
        <!-- 表盘 -->
        <view 
          class="watchface-element"
          style="{{getWatchfaceStyle(theme.config.watchface)}}"
          data-element="{{theme.config.watchface}}"
          bindtap="onSelectElement"
        >
          <text class="time-display">{{mockTime}}</text>
        </view>
        
        <!-- 组件列表 -->
        <view 
          class="widget-element {{selectedElement && selectedElement.id === item.id ? 'selected' : ''}}"
          wx:for="{{theme.config.widgets}}"
          wx:key="id"
          style="{{getWidgetStyle(item)}}"
          data-element="{{item}}"
          bindtap="onSelectElement"
        >
          <!-- 时钟组件 -->
          <view class="clock-widget" wx:if="{{item.type === 'clock'}}">
            <text class="clock-time">{{mockTime}}</text>
          </view>
          
          <!-- 天气组件 -->
          <view class="weather-widget" wx:if="{{item.type === 'weather'}}">
            <van-icon name="{{mockWeather.icon}}" size="20px" />
            <text>{{mockWeather.temperature}}°</text>
          </view>
          
          <!-- 电量组件 -->
          <view class="battery-widget" wx:if="{{item.type === 'battery'}}">
            <van-icon name="battery" size="16px" />
            <text>{{mockBattery}}%</text>
          </view>
          
          <!-- 日期组件 -->
          <view class="date-widget" wx:if="{{item.type === 'date'}}">
            <text>{{mockDate}}</text>
          </view>
          
          <!-- 传感器组件 -->
          <view class="sensor-widget" wx:if="{{item.type === 'sensor'}}">
            <van-icon name="chart" size="16px" />
            <text>{{mockSensorData[item.config.type]}}</text>
          </view>
          
          <!-- 宠物组件 -->
          <view class="pet-widget" wx:if="{{item.type === 'pet'}}">
            <van-icon name="smile-o" size="20px" />
            <text>{{mockPet.name}}</text>
          </view>
          
          <!-- 选中状态边框 -->
          <view class="selection-border" wx:if="{{selectedElement && selectedElement.id === item.id}}"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部工具栏 -->
  <view class="bottom-toolbar" wx:if="{{showToolbar && !previewMode}}">
    <!-- 工具选择 -->
    <view class="tool-tabs">
      <view 
        class="tool-tab {{currentTool === 'select' ? 'active' : ''}}"
        data-tool="select"
        bindtap="onToolChange"
      >
        <van-icon name="pointer" size="18px" />
        <text>选择</text>
      </view>
      <view 
        class="tool-tab {{currentTool === 'widget' ? 'active' : ''}}"
        data-tool="widget"
        bindtap="onToolChange"
      >
        <van-icon name="apps-o" size="18px" />
        <text>组件</text>
      </view>
      <view 
        class="tool-tab {{currentTool === 'background' ? 'active' : ''}}"
        data-tool="background"
        bindtap="onToolChange"
      >
        <van-icon name="photo-o" size="18px" />
        <text>背景</text>
      </view>
    </view>
  </view>

  <!-- 组件面板 -->
  <view class="widget-panel" wx:if="{{currentTool === 'widget' && !previewMode}}">
    <view class="panel-header">
      <text>添加组件</text>
    </view>
    <view class="widget-grid">
      <view 
        class="widget-item"
        wx:for="{{widgets}}"
        wx:key="type"
        data-widget="{{item}}"
        bindtap="onAddWidget"
      >
        <van-icon name="{{item.icon}}" size="24px" />
        <text>{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 背景面板 -->
  <view class="background-panel" wx:if="{{currentTool === 'background' && !previewMode}}">
    <view class="panel-header">
      <text>背景设置</text>
    </view>
    
    <!-- 背景类型 -->
    <view class="background-types">
      <view 
        class="type-item {{theme.config.background.type === item.type ? 'active' : ''}}"
        wx:for="{{backgroundTypes}}"
        wx:key="type"
        data-type="{{item.type}}"
        bindtap="onBackgroundTypeChange"
      >
        <van-icon name="{{item.icon}}" size="20px" />
        <text>{{item.name}}</text>
      </view>
    </view>
    
    <!-- 颜色选择 -->
    <view class="color-picker" wx:if="{{theme.config.background.type === 'color'}}">
      <view class="preset-colors">
        <view 
          class="color-item {{theme.config.background.color === item ? 'active' : ''}}"
          wx:for="{{presetColors}}"
          wx:key="*this"
          style="background-color: {{item}}"
          data-color="{{item}}"
          bindtap="onColorSelect"
        ></view>
      </view>
    </view>
  </view>

  <!-- 属性面板 -->
  <view class="properties-panel" wx:if="{{showProperties && selectedElement && !previewMode}}">
    <view class="panel-header">
      <text>属性设置</text>
      <view class="panel-actions">
        <view class="action-btn" bindtap="onDeleteElement">
          <van-icon name="delete" size="16px" />
        </view>
        <view class="action-btn" bindtap="onCloseProperties">
          <van-icon name="cross" size="16px" />
        </view>
      </view>
    </view>
    
    <!-- 位置设置 -->
    <view class="property-group">
      <view class="group-title">位置</view>
      <view class="property-row">
        <text>X:</text>
        <input 
          type="number"
          value="{{selectedElement.position.x}}"
          bindinput="onPositionXChange"
        />
      </view>
      <view class="property-row">
        <text>Y:</text>
        <input 
          type="number"
          value="{{selectedElement.position.y}}"
          bindinput="onPositionYChange"
        />
      </view>
    </view>
    
    <!-- 大小设置 -->
    <view class="property-group">
      <view class="group-title">大小</view>
      <view class="property-row">
        <text>宽度:</text>
        <input 
          type="number"
          value="{{selectedElement.size.width}}"
          bindinput="onWidthChange"
        />
      </view>
      <view class="property-row">
        <text>高度:</text>
        <input 
          type="number"
          value="{{selectedElement.size.height}}"
          bindinput="onHeightChange"
        />
      </view>
    </view>
    
    <!-- 颜色设置 -->
    <view class="property-group">
      <view class="group-title">颜色</view>
      <view class="color-picker">
        <view 
          class="color-item {{selectedElement.config.color === item ? 'active' : ''}}"
          wx:for="{{presetColors}}"
          wx:key="*this"
          style="background-color: {{item}}"
          data-color="{{item}}"
          bindtap="onElementColorSelect"
        ></view>
      </view>
    </view>
  </view>

  <!-- 预览模式遮罩 -->
  <view class="preview-overlay" wx:if="{{previewMode}}" bindtap="onPreview">
    <view class="preview-tip">
      <text>预览模式 - 点击退出</text>
    </view>
  </view>
</view>
