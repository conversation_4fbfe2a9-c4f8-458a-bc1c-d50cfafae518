#!/usr/bin/env python3
"""
TIMO项目集成测试脚本
用于验证整个系统的功能完整性和性能
"""

import os
import sys
import json
import time
import requests
import subprocess
import threading
from datetime import datetime
from typing import Dict, List, Optional

class TIMOIntegrationTest:
    def __init__(self):
        self.config = {
            'api_base': 'http://localhost:3000',
            'ws_base': 'ws://localhost:3000',
            'test_timeout': 30,
            'device_serial_port': '/dev/ttyUSB0',
            'test_results': []
        }
        
        self.test_stats = {
            'total': 0,
            'passed': 0,
            'failed': 0,
            'skipped': 0
        }
    
    def log(self, message: str, level: str = 'INFO'):
        """记录测试日志"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] [{level}] {message}")
    
    def run_test(self, test_name: str, test_func, *args, **kwargs):
        """运行单个测试"""
        self.test_stats['total'] += 1
        self.log(f"开始测试: {test_name}")
        
        try:
            start_time = time.time()
            result = test_func(*args, **kwargs)
            end_time = time.time()
            
            if result:
                self.test_stats['passed'] += 1
                self.log(f"测试通过: {test_name} (耗时: {end_time - start_time:.2f}s)", 'PASS')
            else:
                self.test_stats['failed'] += 1
                self.log(f"测试失败: {test_name}", 'FAIL')
            
            self.test_results.append({
                'name': test_name,
                'result': 'PASS' if result else 'FAIL',
                'duration': end_time - start_time,
                'timestamp': datetime.now().isoformat()
            })
            
            return result
            
        except Exception as e:
            self.test_stats['failed'] += 1
            self.log(f"测试异常: {test_name} - {str(e)}", 'ERROR')
            
            self.test_results.append({
                'name': test_name,
                'result': 'ERROR',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            
            return False
    
    def test_project_structure(self) -> bool:
        """测试项目结构完整性"""
        required_dirs = [
            '1_main_device_firmware',
            '2_base_station_firmware', 
            '3_cloud_service',
            '4_wechat_miniprogram'
        ]
        
        for dir_name in required_dirs:
            if not os.path.exists(dir_name):
                self.log(f"缺少目录: {dir_name}", 'ERROR')
                return False
        
        # 检查关键文件
        key_files = [
            '1_main_device_firmware/CMakeLists.txt',
            '2_base_station_firmware/CMakeLists.txt',
            '3_cloud_service/package.json',
            '4_wechat_miniprogram/app.json'
        ]
        
        for file_path in key_files:
            if not os.path.exists(file_path):
                self.log(f"缺少文件: {file_path}", 'ERROR')
                return False
        
        return True
    
    def test_firmware_compilation(self) -> bool:
        """测试固件编译"""
        try:
            # 检查ESP-IDF环境
            result = subprocess.run(['idf.py', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                self.log("ESP-IDF环境未配置", 'WARN')
                return True  # 跳过测试但不标记为失败
            
            # 测试主体固件编译
            os.chdir('1_main_device_firmware')
            result = subprocess.run(['idf.py', 'build'], 
                                  capture_output=True, text=True, timeout=300)
            if result.returncode != 0:
                self.log(f"主体固件编译失败: {result.stderr}", 'ERROR')
                return False
            
            os.chdir('..')
            
            # 测试底座固件编译
            os.chdir('2_base_station_firmware')
            result = subprocess.run(['idf.py', 'build'], 
                                  capture_output=True, text=True, timeout=300)
            if result.returncode != 0:
                self.log(f"底座固件编译失败: {result.stderr}", 'ERROR')
                return False
            
            os.chdir('..')
            return True
            
        except subprocess.TimeoutExpired:
            self.log("固件编译超时", 'ERROR')
            return False
        except Exception as e:
            self.log(f"固件编译测试异常: {str(e)}", 'ERROR')
            return False
    
    def test_cloud_service_startup(self) -> bool:
        """测试云端服务启动"""
        try:
            os.chdir('3_cloud_service')
            
            # 检查依赖
            if not os.path.exists('node_modules'):
                self.log("安装云端服务依赖...")
                result = subprocess.run(['npm', 'install'], 
                                      capture_output=True, text=True, timeout=120)
                if result.returncode != 0:
                    self.log(f"依赖安装失败: {result.stderr}", 'ERROR')
                    return False
            
            # 启动服务（后台）
            self.log("启动云端服务...")
            self.cloud_process = subprocess.Popen(['npm', 'start'], 
                                                stdout=subprocess.PIPE, 
                                                stderr=subprocess.PIPE)
            
            # 等待服务启动
            time.sleep(10)
            
            # 检查服务是否运行
            if self.cloud_process.poll() is not None:
                self.log("云端服务启动失败", 'ERROR')
                return False
            
            os.chdir('..')
            return True
            
        except Exception as e:
            self.log(f"云端服务启动测试异常: {str(e)}", 'ERROR')
            return False
    
    def test_api_endpoints(self) -> bool:
        """测试API接口"""
        try:
            # 健康检查
            response = requests.get(f"{self.config['api_base']}/health", timeout=10)
            if response.status_code != 200:
                self.log(f"健康检查失败: {response.status_code}", 'ERROR')
                return False
            
            # 测试用户注册
            user_data = {
                'username': 'test_user',
                'email': '<EMAIL>',
                'password': 'test123456'
            }
            
            response = requests.post(f"{self.config['api_base']}/api/user/register", 
                                   json=user_data, timeout=10)
            if response.status_code not in [200, 201, 400]:  # 400可能是用户已存在
                self.log(f"用户注册测试失败: {response.status_code}", 'ERROR')
                return False
            
            # 测试用户登录
            login_data = {
                'email': '<EMAIL>',
                'password': 'test123456'
            }
            
            response = requests.post(f"{self.config['api_base']}/api/user/login", 
                                   json=login_data, timeout=10)
            if response.status_code != 200:
                self.log(f"用户登录测试失败: {response.status_code}", 'ERROR')
                return False
            
            # 获取token用于后续测试
            self.auth_token = response.json().get('token')
            if not self.auth_token:
                self.log("未获取到认证token", 'ERROR')
                return False
            
            return True
            
        except requests.RequestException as e:
            self.log(f"API测试网络异常: {str(e)}", 'ERROR')
            return False
        except Exception as e:
            self.log(f"API测试异常: {str(e)}", 'ERROR')
            return False
    
    def test_device_management(self) -> bool:
        """测试设备管理功能"""
        try:
            headers = {'Authorization': f'Bearer {self.auth_token}'}
            
            # 测试设备注册
            device_data = {
                'deviceId': 'test_device_001',
                'deviceType': 'main',
                'name': '测试主体设备',
                'hardware': {
                    'chipModel': 'ESP32-S3',
                    'macAddress': '00:11:22:33:44:55',
                    'firmwareVersion': '1.0.0'
                }
            }
            
            response = requests.post(f"{self.config['api_base']}/api/device/register", 
                                   json=device_data, headers=headers, timeout=10)
            if response.status_code not in [200, 201, 400]:
                self.log(f"设备注册测试失败: {response.status_code}", 'ERROR')
                return False
            
            # 测试获取设备列表
            response = requests.get(f"{self.config['api_base']}/api/device", 
                                  headers=headers, timeout=10)
            if response.status_code != 200:
                self.log(f"获取设备列表失败: {response.status_code}", 'ERROR')
                return False
            
            devices = response.json().get('devices', [])
            if not devices:
                self.log("设备列表为空", 'WARN')
            
            return True
            
        except Exception as e:
            self.log(f"设备管理测试异常: {str(e)}", 'ERROR')
            return False
    
    def test_task_management(self) -> bool:
        """测试任务管理功能"""
        try:
            headers = {'Authorization': f'Bearer {self.auth_token}'}
            
            # 测试创建任务
            task_data = {
                'title': '测试任务',
                'description': '这是一个测试任务',
                'priority': 'normal',
                'type': 'general'
            }
            
            response = requests.post(f"{self.config['api_base']}/api/task", 
                                   json=task_data, headers=headers, timeout=10)
            if response.status_code not in [200, 201]:
                self.log(f"创建任务失败: {response.status_code}", 'ERROR')
                return False
            
            task_id = response.json().get('task', {}).get('id')
            if not task_id:
                self.log("未获取到任务ID", 'ERROR')
                return False
            
            # 测试获取任务列表
            response = requests.get(f"{self.config['api_base']}/api/task", 
                                  headers=headers, timeout=10)
            if response.status_code != 200:
                self.log(f"获取任务列表失败: {response.status_code}", 'ERROR')
                return False
            
            # 测试完成任务
            response = requests.post(f"{self.config['api_base']}/api/task/{task_id}/complete", 
                                   headers=headers, timeout=10)
            if response.status_code != 200:
                self.log(f"完成任务失败: {response.status_code}", 'ERROR')
                return False
            
            return True
            
        except Exception as e:
            self.log(f"任务管理测试异常: {str(e)}", 'ERROR')
            return False
    
    def test_miniprogram_config(self) -> bool:
        """测试小程序配置"""
        try:
            # 检查小程序配置文件
            config_files = [
                '4_wechat_miniprogram/app.json',
                '4_wechat_miniprogram/app.js',
                '4_wechat_miniprogram/project.config.json'
            ]
            
            for config_file in config_files:
                if not os.path.exists(config_file):
                    self.log(f"小程序配置文件缺失: {config_file}", 'ERROR')
                    return False
            
            # 检查app.json配置
            with open('4_wechat_miniprogram/app.json', 'r', encoding='utf-8') as f:
                app_config = json.load(f)
            
            required_pages = ['pages/index/index', 'pages/login/login']
            for page in required_pages:
                if page not in app_config.get('pages', []):
                    self.log(f"小程序缺少必要页面: {page}", 'ERROR')
                    return False
            
            return True
            
        except Exception as e:
            self.log(f"小程序配置测试异常: {str(e)}", 'ERROR')
            return False
    
    def test_performance(self) -> bool:
        """性能测试"""
        try:
            # API响应时间测试
            start_time = time.time()
            response = requests.get(f"{self.config['api_base']}/health", timeout=10)
            response_time = time.time() - start_time
            
            if response_time > 1.0:  # 响应时间超过1秒
                self.log(f"API响应时间过长: {response_time:.2f}s", 'WARN')
            
            # 并发测试
            def concurrent_request():
                try:
                    requests.get(f"{self.config['api_base']}/health", timeout=5)
                    return True
                except:
                    return False
            
            threads = []
            for i in range(10):
                thread = threading.Thread(target=concurrent_request)
                threads.append(thread)
                thread.start()
            
            for thread in threads:
                thread.join()
            
            return True
            
        except Exception as e:
            self.log(f"性能测试异常: {str(e)}", 'ERROR')
            return False
    
    def cleanup(self):
        """清理测试环境"""
        try:
            # 停止云端服务
            if hasattr(self, 'cloud_process'):
                self.cloud_process.terminate()
                self.cloud_process.wait(timeout=10)
                self.log("云端服务已停止")
        except:
            pass
    
    def generate_report(self):
        """生成测试报告"""
        report = {
            'test_summary': self.test_stats,
            'test_results': self.test_results,
            'timestamp': datetime.now().isoformat(),
            'environment': {
                'python_version': sys.version,
                'platform': sys.platform
            }
        }
        
        # 保存JSON报告
        with open('test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 生成HTML报告
        html_report = self.generate_html_report(report)
        with open('test_report.html', 'w', encoding='utf-8') as f:
            f.write(html_report)
        
        self.log(f"测试报告已生成: test_report.json, test_report.html")
    
    def generate_html_report(self, report: Dict) -> str:
        """生成HTML测试报告"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>TIMO集成测试报告</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background: #2E86AB; color: white; padding: 20px; border-radius: 5px; }}
                .summary {{ margin: 20px 0; padding: 15px; background: #f5f5f5; border-radius: 5px; }}
                .test-result {{ margin: 10px 0; padding: 10px; border-left: 4px solid #ddd; }}
                .pass {{ border-left-color: #52c41a; }}
                .fail {{ border-left-color: #ff4d4f; }}
                .error {{ border-left-color: #fa8c16; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>TIMO集成测试报告</h1>
                <p>生成时间: {report['timestamp']}</p>
            </div>
            
            <div class="summary">
                <h2>测试摘要</h2>
                <p>总计: {report['test_summary']['total']}</p>
                <p>通过: {report['test_summary']['passed']}</p>
                <p>失败: {report['test_summary']['failed']}</p>
                <p>跳过: {report['test_summary']['skipped']}</p>
                <p>成功率: {(report['test_summary']['passed'] / max(report['test_summary']['total'], 1) * 100):.1f}%</p>
            </div>
            
            <h2>测试详情</h2>
            <table>
                <tr>
                    <th>测试名称</th>
                    <th>结果</th>
                    <th>耗时</th>
                    <th>时间</th>
                </tr>
        """
        
        for result in report['test_results']:
            css_class = result['result'].lower()
            duration = result.get('duration', 0)
            html += f"""
                <tr class="{css_class}">
                    <td>{result['name']}</td>
                    <td>{result['result']}</td>
                    <td>{duration:.2f}s</td>
                    <td>{result['timestamp']}</td>
                </tr>
            """
        
        html += """
            </table>
        </body>
        </html>
        """
        
        return html
    
    def run_all_tests(self):
        """运行所有测试"""
        self.log("开始TIMO项目集成测试")
        
        try:
            # 运行测试套件
            self.run_test("项目结构检查", self.test_project_structure)
            self.run_test("固件编译测试", self.test_firmware_compilation)
            self.run_test("云端服务启动", self.test_cloud_service_startup)
            self.run_test("API接口测试", self.test_api_endpoints)
            self.run_test("设备管理测试", self.test_device_management)
            self.run_test("任务管理测试", self.test_task_management)
            self.run_test("小程序配置测试", self.test_miniprogram_config)
            self.run_test("性能测试", self.test_performance)
            
        finally:
            self.cleanup()
            self.generate_report()
            
            # 输出测试结果
            self.log("=" * 50)
            self.log("测试完成")
            self.log(f"总计: {self.test_stats['total']}")
            self.log(f"通过: {self.test_stats['passed']}")
            self.log(f"失败: {self.test_stats['failed']}")
            self.log(f"跳过: {self.test_stats['skipped']}")
            
            success_rate = (self.test_stats['passed'] / max(self.test_stats['total'], 1)) * 100
            self.log(f"成功率: {success_rate:.1f}%")
            
            if self.test_stats['failed'] > 0:
                self.log("存在测试失败，请检查日志", 'WARN')
                return False
            else:
                self.log("所有测试通过", 'PASS')
                return True

if __name__ == '__main__':
    tester = TIMOIntegrationTest()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
