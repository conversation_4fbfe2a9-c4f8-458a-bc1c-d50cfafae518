#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TIMO智能闹钟项目集成测试脚本
<AUTHOR> Team
@version 1.0.0
"""

import os
import sys
import json
import time
import subprocess
import requests
from datetime import datetime
from pathlib import Path

class TIMOIntegrationTest:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {
                'total': 0,
                'passed': 0,
                'failed': 0,
                'skipped': 0
            }
        }
        
    def log(self, message, level='INFO'):
        """日志输出"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] [{level}] {message}")
        
    def run_command(self, command, cwd=None, timeout=30):
        """执行命令"""
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd or self.project_root,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", "Command timeout"
        except Exception as e:
            return False, "", str(e)
    
    def test_project_structure(self):
        """测试项目结构完整性"""
        self.log("测试项目结构...")
        
        required_dirs = [
            '1_main_device_firmware',
            '2_base_station_firmware', 
            '3_cloud_service',
            '4_wechat_miniprogram'
        ]
        
        missing_dirs = []
        for dir_name in required_dirs:
            if not (self.project_root / dir_name).exists():
                missing_dirs.append(dir_name)
        
        # 检查关键文件
        key_files = [
            '1_main_device_firmware/CMakeLists.txt',
            '2_base_station_firmware/CMakeLists.txt',
            '3_cloud_service/package.json',
            '4_wechat_miniprogram/app.json'
        ]
        
        missing_files = []
        for file_path in key_files:
            if not (self.project_root / file_path).exists():
                missing_files.append(file_path)
        
        success = len(missing_dirs) == 0 and len(missing_files) == 0
        
        result = {
            'success': success,
            'missing_dirs': missing_dirs,
            'missing_files': missing_files,
            'message': '项目结构完整' if success else f'缺少目录: {missing_dirs}, 缺少文件: {missing_files}'
        }
        
        self.test_results['tests']['project_structure'] = result
        return success
    
    def test_main_device_firmware(self):
        """测试主体设备固件"""
        self.log("测试主体设备固件...")
        
        firmware_dir = self.project_root / '1_main_device_firmware'
        
        # 检查ESP-IDF环境
        success, stdout, stderr = self.run_command('idf.py --version', cwd=firmware_dir)
        if not success:
            result = {
                'success': False,
                'message': 'ESP-IDF环境未配置',
                'error': stderr
            }
            self.test_results['tests']['main_device_firmware'] = result
            return False
        
        # 检查配置文件
        config_files = ['sdkconfig.defaults', 'partitions.csv']
        missing_configs = []
        for config_file in config_files:
            if not (firmware_dir / config_file).exists():
                missing_configs.append(config_file)
        
        # 尝试编译检查（仅检查语法）
        self.log("检查主体固件编译配置...")
        success, stdout, stderr = self.run_command(
            'idf.py reconfigure', 
            cwd=firmware_dir,
            timeout=60
        )
        
        compile_success = success and 'Project build complete' not in stderr
        
        result = {
            'success': len(missing_configs) == 0 and compile_success,
            'missing_configs': missing_configs,
            'compile_check': compile_success,
            'message': '主体固件配置正常' if len(missing_configs) == 0 and compile_success else '主体固件配置有问题'
        }
        
        self.test_results['tests']['main_device_firmware'] = result
        return result['success']
    
    def test_base_station_firmware(self):
        """测试底座设备固件"""
        self.log("测试底座设备固件...")
        
        firmware_dir = self.project_root / '2_base_station_firmware'
        
        # 检查ESP-IDF环境
        success, stdout, stderr = self.run_command('idf.py --version', cwd=firmware_dir)
        if not success:
            result = {
                'success': False,
                'message': 'ESP-IDF环境未配置',
                'error': stderr
            }
            self.test_results['tests']['base_station_firmware'] = result
            return False
        
        # 检查配置文件
        config_files = ['sdkconfig.defaults']
        missing_configs = []
        for config_file in config_files:
            if not (firmware_dir / config_file).exists():
                missing_configs.append(config_file)
        
        # 尝试编译检查
        self.log("检查底座固件编译配置...")
        success, stdout, stderr = self.run_command(
            'idf.py reconfigure', 
            cwd=firmware_dir,
            timeout=60
        )
        
        compile_success = success
        
        result = {
            'success': len(missing_configs) == 0 and compile_success,
            'missing_configs': missing_configs,
            'compile_check': compile_success,
            'message': '底座固件配置正常' if len(missing_configs) == 0 and compile_success else '底座固件配置有问题'
        }
        
        self.test_results['tests']['base_station_firmware'] = result
        return result['success']
    
    def test_cloud_service(self):
        """测试云端服务"""
        self.log("测试云端服务...")
        
        service_dir = self.project_root / '3_cloud_service'
        
        # 检查Node.js环境
        success, stdout, stderr = self.run_command('node --version')
        if not success:
            result = {
                'success': False,
                'message': 'Node.js环境未安装',
                'error': stderr
            }
            self.test_results['tests']['cloud_service'] = result
            return False
        
        # 检查package.json
        package_json = service_dir / 'package.json'
        if not package_json.exists():
            result = {
                'success': False,
                'message': 'package.json文件不存在'
            }
            self.test_results['tests']['cloud_service'] = result
            return False
        
        # 检查依赖安装
        self.log("检查云端服务依赖...")
        success, stdout, stderr = self.run_command(
            'npm list --depth=0', 
            cwd=service_dir,
            timeout=30
        )
        
        dependencies_ok = success or 'missing' not in stderr.lower()
        
        # 检查关键文件
        key_files = [
            'src/app.js',
            'src/routes/device.js',
            'src/routes/data.js',
            'src/routes/voice.js',
            'src/models/User.js'
        ]
        
        missing_files = []
        for file_path in key_files:
            if not (service_dir / file_path).exists():
                missing_files.append(file_path)
        
        result = {
            'success': dependencies_ok and len(missing_files) == 0,
            'dependencies_ok': dependencies_ok,
            'missing_files': missing_files,
            'message': '云端服务配置正常' if dependencies_ok and len(missing_files) == 0 else '云端服务配置有问题'
        }
        
        self.test_results['tests']['cloud_service'] = result
        return result['success']
    
    def test_wechat_miniprogram(self):
        """测试微信小程序"""
        self.log("测试微信小程序...")
        
        miniprogram_dir = self.project_root / '4_wechat_miniprogram'
        
        # 检查app.json
        app_json = miniprogram_dir / 'app.json'
        if not app_json.exists():
            result = {
                'success': False,
                'message': 'app.json文件不存在'
            }
            self.test_results['tests']['wechat_miniprogram'] = result
            return False
        
        # 解析app.json
        try:
            with open(app_json, 'r', encoding='utf-8') as f:
                app_config = json.load(f)
        except Exception as e:
            result = {
                'success': False,
                'message': f'app.json解析失败: {str(e)}'
            }
            self.test_results['tests']['wechat_miniprogram'] = result
            return False
        
        # 检查页面文件
        pages = app_config.get('pages', [])
        missing_pages = []
        
        for page in pages:
            page_js = miniprogram_dir / f'{page}.js'
            page_wxml = miniprogram_dir / f'{page}.wxml'
            
            if not page_js.exists():
                missing_pages.append(f'{page}.js')
            if not page_wxml.exists():
                missing_pages.append(f'{page}.wxml')
        
        # 检查工具文件
        utils_files = ['utils/api.js', 'utils/auth.js', 'utils/websocket.js']
        missing_utils = []
        
        for util_file in utils_files:
            if not (miniprogram_dir / util_file).exists():
                missing_utils.append(util_file)
        
        result = {
            'success': len(missing_pages) == 0 and len(missing_utils) == 0,
            'pages_count': len(pages),
            'missing_pages': missing_pages,
            'missing_utils': missing_utils,
            'message': '微信小程序配置正常' if len(missing_pages) == 0 and len(missing_utils) == 0 else '微信小程序配置有问题'
        }
        
        self.test_results['tests']['wechat_miniprogram'] = result
        return result['success']
    
    def test_documentation(self):
        """测试文档完整性"""
        self.log("测试文档完整性...")
        
        required_docs = [
            'README.md',
            'PROJECT_OVERVIEW.md',
            'DEVELOPMENT_GUIDE.md',
            'PROJECT_STATUS.md',
            'DEPLOYMENT.md'
        ]
        
        missing_docs = []
        for doc in required_docs:
            if not (self.project_root / doc).exists():
                missing_docs.append(doc)
        
        result = {
            'success': len(missing_docs) == 0,
            'missing_docs': missing_docs,
            'message': '文档完整' if len(missing_docs) == 0 else f'缺少文档: {missing_docs}'
        }
        
        self.test_results['tests']['documentation'] = result
        return result['success']
    
    def run_all_tests(self):
        """运行所有测试"""
        self.log("开始TIMO项目集成测试...")
        
        tests = [
            ('项目结构', self.test_project_structure),
            ('主体固件', self.test_main_device_firmware),
            ('底座固件', self.test_base_station_firmware),
            ('云端服务', self.test_cloud_service),
            ('微信小程序', self.test_wechat_miniprogram),
            ('文档完整性', self.test_documentation)
        ]
        
        for test_name, test_func in tests:
            self.log(f"运行测试: {test_name}")
            try:
                success = test_func()
                if success:
                    self.test_results['summary']['passed'] += 1
                    self.log(f"✅ {test_name} - 通过", 'SUCCESS')
                else:
                    self.test_results['summary']['failed'] += 1
                    self.log(f"❌ {test_name} - 失败", 'ERROR')
            except Exception as e:
                self.test_results['summary']['failed'] += 1
                self.log(f"❌ {test_name} - 异常: {str(e)}", 'ERROR')
            
            self.test_results['summary']['total'] += 1
        
        # 生成测试报告
        self.generate_report()
        
        # 输出总结
        summary = self.test_results['summary']
        self.log(f"测试完成: 总计 {summary['total']}, 通过 {summary['passed']}, 失败 {summary['failed']}")
        
        return summary['failed'] == 0
    
    def generate_report(self):
        """生成测试报告"""
        report_file = self.project_root / 'integration_test_report.json'
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, ensure_ascii=False, indent=2)
            
            self.log(f"测试报告已生成: {report_file}")
        except Exception as e:
            self.log(f"生成测试报告失败: {str(e)}", 'ERROR')

def main():
    """主函数"""
    tester = TIMOIntegrationTest()
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
