/**
 * 主题管理页面
 * <AUTHOR> Team
 * @version 1.0.0
 */

const app = getApp();
const api = require('../../utils/api');

Page({
  data: {
    // 主题列表
    themes: [],
    
    // 当前选中主题
    currentTheme: null,
    
    // 页面状态
    loading: true,
    refreshing: false,
    
    // 显示模式
    showMode: 'list', // list, design, preview
    
    // 主题分类
    categories: [
      { id: 'all', name: '全部', icon: 'apps-o' },
      { id: 'official', name: '官方', icon: 'star-o' },
      { id: 'community', name: '社区', icon: 'friends-o' },
      { id: 'my', name: '我的', icon: 'user-o' }
    ],
    currentCategory: 'all',
    
    // 搜索
    searchKeyword: '',
    showSearch: false
  },

  /**
   * 页面加载
   */
  onLoad() {
    console.log('主题页面加载');
    this.initPage();
  },

  /**
   * 页面显示
   */
  onShow() {
    console.log('主题页面显示');
    this.refreshThemes();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshThemes(true);
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 检查登录状态
      if (!app.globalData.token) {
        wx.reLaunch({
          url: '/pages/login/login'
        });
        return;
      }

      // 加载主题列表
      await this.loadThemes();
    } catch (error) {
      console.error('初始化主题页面失败:', error);
      app.showError('页面加载失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 刷新主题列表
   */
  async refreshThemes(isPullRefresh = false) {
    if (isPullRefresh) {
      this.setData({ refreshing: true });
    }

    try {
      await this.loadThemes();
    } catch (error) {
      console.error('刷新主题列表失败:', error);
      app.showError('刷新失败');
    } finally {
      if (isPullRefresh) {
        this.setData({ refreshing: false });
        wx.stopPullDownRefresh();
      }
    }
  },

  /**
   * 加载主题列表
   */
  async loadThemes() {
    try {
      const params = {
        category: this.data.currentCategory,
        keyword: this.data.searchKeyword
      };

      const themes = await api.getThemes(params);
      
      this.setData({ 
        themes,
        currentTheme: app.globalData.currentTheme
      });

      console.log('主题列表加载完成:', themes.length);
    } catch (error) {
      console.error('加载主题列表失败:', error);
      throw error;
    }
  },

  /**
   * 切换分类
   */
  onCategoryChange(e) {
    const { category } = e.currentTarget.dataset;
    
    if (category === this.data.currentCategory) {
      return;
    }

    this.setData({ 
      currentCategory: category,
      loading: true 
    });

    this.loadThemes().finally(() => {
      this.setData({ loading: false });
    });
  },

  /**
   * 显示搜索
   */
  onShowSearch() {
    this.setData({ showSearch: true });
  },

  /**
   * 隐藏搜索
   */
  onHideSearch() {
    this.setData({ 
      showSearch: false,
      searchKeyword: ''
    });
    
    if (this.data.searchKeyword) {
      this.loadThemes();
    }
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    this.setData({ searchKeyword: e.detail.value });
  },

  /**
   * 执行搜索
   */
  onSearch() {
    this.setData({ loading: true });
    this.loadThemes().finally(() => {
      this.setData({ loading: false });
    });
  },

  /**
   * 预览主题
   */
  onPreviewTheme(e) {
    const { theme } = e.currentTarget.dataset;
    
    wx.navigateTo({
      url: `/pages/theme/preview?themeId=${theme.id}`
    });
  },

  /**
   * 应用主题
   */
  async onApplyTheme(e) {
    const { theme } = e.currentTarget.dataset;
    
    try {
      app.showLoading('应用主题中...');
      
      await api.applyTheme(theme.id);
      
      // 更新全局当前主题
      app.globalData.currentTheme = theme;
      this.setData({ currentTheme: theme });
      
      app.showSuccess('主题应用成功');
    } catch (error) {
      console.error('应用主题失败:', error);
      app.showError('应用失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 下载主题
   */
  async onDownloadTheme(e) {
    const { theme } = e.currentTarget.dataset;
    
    try {
      app.showLoading('下载主题中...');
      
      await api.downloadTheme(theme.id);
      
      // 刷新主题列表
      await this.loadThemes();
      
      app.showSuccess('主题下载成功');
    } catch (error) {
      console.error('下载主题失败:', error);
      app.showError('下载失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 删除主题
   */
  async onDeleteTheme(e) {
    const { theme } = e.currentTarget.dataset;
    
    const result = await new Promise(resolve => {
      wx.showModal({
        title: '确认删除',
        content: `确定要删除主题"${theme.name}"吗？`,
        success: resolve
      });
    });

    if (!result.confirm) {
      return;
    }

    try {
      app.showLoading('删除主题中...');
      
      await api.deleteTheme(theme.id);
      
      // 刷新主题列表
      await this.loadThemes();
      
      app.showSuccess('主题删除成功');
    } catch (error) {
      console.error('删除主题失败:', error);
      app.showError('删除失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 创建新主题
   */
  onCreateTheme() {
    wx.navigateTo({
      url: '/pages/theme/designer'
    });
  },

  /**
   * 编辑主题
   */
  onEditTheme(e) {
    const { theme } = e.currentTarget.dataset;
    
    wx.navigateTo({
      url: `/pages/theme/designer?themeId=${theme.id}`
    });
  },

  /**
   * 分享主题
   */
  onShareTheme(e) {
    const { theme } = e.currentTarget.dataset;
    
    return {
      title: `TIMO主题：${theme.name}`,
      path: `/pages/theme/preview?themeId=${theme.id}`,
      imageUrl: theme.preview
    };
  }
});
