/* 首页样式 */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #fff;
  font-size: 28rpx;
  margin-top: 20rpx;
}

/* 内容区域 */
.content {
  padding-bottom: 40rpx;
}

/* 用户信息栏 */
.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-text {
  display: flex;
  flex-direction: column;
}

.greeting {
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.time {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

.weather-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.weather {
  color: #fff;
  font-size: 36rpx;
  font-weight: 300;
}

.weather-desc {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

/* 卡片通用样式 */
.device-card,
.quick-actions,
.today-tasks,
.pet-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.more-btn {
  color: #2E86AB;
  font-size: 28rpx;
}

/* 设备卡片 */
.device-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.device-selector {
  display: flex;
  align-items: center;
}

.device-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-right: 10rpx;
}

.device-arrow {
  color: #999;
  font-size: 24rpx;
}

.device-status {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.device-status.online .status-dot {
  background: #52c41a;
}

.device-status.offline .status-dot {
  background: #ff4d4f;
}

.status-text {
  font-size: 24rpx;
  color: #666;
}

/* 传感器数据 */
.sensor-data {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.sensor-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.sensor-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.sensor-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 无设备状态 */
.no-device {
  text-align: center;
  padding: 40rpx 0;
}

.no-device-text {
  color: #999;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.bind-device-btn {
  background: #2E86AB;
  color: #fff;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 快捷功能 */
.actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 12rpx;
}

.action-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

/* 任务列表 */
.task-list {
  margin-bottom: 20rpx;
}

.task-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.task-item:last-child {
  border-bottom: none;
}

.task-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.task-checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 50%;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-checkbox.checked {
  background: #52c41a;
  border-color: #52c41a;
}

.check-icon {
  color: #fff;
  font-size: 24rpx;
}

.task-text {
  display: flex;
  flex-direction: column;
}

.task-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.task-title.completed {
  text-decoration: line-through;
  color: #999;
}

.task-time {
  font-size: 24rpx;
  color: #999;
}

.task-priority {
  width: 8rpx;
  height: 40rpx;
  border-radius: 4rpx;
}

.priority-low {
  background: #52c41a;
}

.priority-normal {
  background: #1890ff;
}

.priority-high {
  background: #fa8c16;
}

.priority-urgent {
  background: #ff4d4f;
}

/* 无任务状态 */
.no-tasks {
  text-align: center;
  padding: 40rpx 0;
}

.no-tasks-text {
  color: #999;
  font-size: 28rpx;
}

/* 任务统计 */
.task-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #2E86AB;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 宠物卡片 */
.pet-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.pet-avatar {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin-right: 20rpx;
}

.pet-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.pet-emotion {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 4rpx solid #fff;
}

.emotion-happy {
  background: #52c41a;
}

.emotion-sad {
  background: #1890ff;
}

.emotion-tired {
  background: #fa8c16;
}

.emotion-neutral {
  background: #d9d9d9;
}

.pet-details {
  flex: 1;
}

.pet-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.pet-level {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
}

/* 宠物属性 */
.pet-attributes {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.attribute-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.attr-label {
  font-size: 24rpx;
  color: #666;
  width: 60rpx;
}

.attr-bar {
  flex: 1;
  height: 12rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
}

.attr-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a, #73d13d);
  transition: width 0.3s ease;
}

.attr-value {
  font-size: 24rpx;
  color: #333;
  width: 60rpx;
  text-align: right;
}

/* 宠物交互按钮 */
.pet-actions {
  display: flex;
  gap: 20rpx;
}

.pet-action-btn {
  flex: 1;
  background: #f8f9fa;
  color: #333;
  border-radius: 50rpx;
  padding: 20rpx;
  font-size: 26rpx;
  border: none;
}

.pet-action-btn:active {
  background: #e6f7ff;
}
