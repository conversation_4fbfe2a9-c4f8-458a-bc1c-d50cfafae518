/**
 * 设备服务
 * <AUTHOR> Team
 * @version 1.0.0
 */

const DeviceData = require('../models/DeviceData');
const SensorData = require('../models/SensorData');
const logger = require('../utils/logger');

class DeviceService {
  /**
   * 注册新设备
   */
  async registerDevice(deviceInfo, userId) {
    try {
      const {
        deviceId,
        name,
        type,
        model,
        hardwareVersion,
        firmwareVersion
      } = deviceInfo;

      // 检查设备是否已存在
      const existingDevice = await DeviceData.findOne({ deviceId });
      if (existingDevice) {
        throw new Error('Device already registered');
      }

      // 创建设备记录
      const device = new DeviceData({
        deviceId,
        userId,
        name,
        type,
        model,
        hardwareVersion,
        firmwareVersion,
        status: 'offline'
      });

      await device.save();
      logger.info(`Device registered: ${deviceId} for user ${userId}`);

      return device;
    } catch (error) {
      logger.error('Register device error:', error);
      throw error;
    }
  }

  /**
   * 设备认证
   */
  async authenticateDevice(deviceId, authToken) {
    try {
      const device = await DeviceData.findOne({ deviceId });
      if (!device) {
        throw new Error('Device not found');
      }

      // TODO: 验证认证令牌
      // 这里应该实现设备认证逻辑

      return device;
    } catch (error) {
      logger.error('Authenticate device error:', error);
      throw error;
    }
  }

  /**
   * 更新设备状态
   */
  async updateDeviceStatus(deviceId, status, metadata = {}) {
    try {
      const device = await DeviceData.findOne({ deviceId });
      if (!device) {
        throw new Error('Device not found');
      }

      await device.updateStatus(status);

      // 更新额外的元数据
      if (metadata.firmwareVersion) {
        device.firmwareVersion = metadata.firmwareVersion;
      }
      if (metadata.config) {
        Object.assign(device.config, metadata.config);
      }
      if (metadata.stats) {
        Object.assign(device.stats, metadata.stats);
      }

      await device.save();

      logger.info(`Device status updated: ${deviceId} -> ${status}`);
      return device;
    } catch (error) {
      logger.error('Update device status error:', error);
      throw error;
    }
  }

  /**
   * 获取用户设备列表
   */
  async getUserDevices(userId, type = null) {
    try {
      return await DeviceData.getByUser(userId, type);
    } catch (error) {
      logger.error('Get user devices error:', error);
      throw error;
    }
  }

  /**
   * 获取设备详情
   */
  async getDeviceDetail(deviceId, userId) {
    try {
      const device = await DeviceData.findOne({ deviceId, userId });
      if (!device) {
        throw new Error('Device not found');
      }

      // 获取最新的传感器数据
      const latestSensorData = await SensorData.getLatestByDevice(deviceId);

      return {
        ...device.toObject(),
        latestSensorData
      };
    } catch (error) {
      logger.error('Get device detail error:', error);
      throw error;
    }
  }

  /**
   * 更新设备配置
   */
  async updateDeviceConfig(deviceId, userId, configPath, value) {
    try {
      const device = await DeviceData.findOne({ deviceId, userId });
      if (!device) {
        throw new Error('Device not found');
      }

      await device.updateConfig(configPath, value);
      logger.info(`Device config updated: ${deviceId} ${configPath} = ${value}`);

      return device;
    } catch (error) {
      logger.error('Update device config error:', error);
      throw error;
    }
  }

  /**
   * 删除设备
   */
  async deleteDevice(deviceId, userId) {
    try {
      const device = await DeviceData.findOne({ deviceId, userId });
      if (!device) {
        throw new Error('Device not found');
      }

      // 删除相关的传感器数据
      await SensorData.deleteMany({ deviceId, userId });

      // 删除设备记录
      await device.remove();

      logger.info(`Device deleted: ${deviceId}`);
      return true;
    } catch (error) {
      logger.error('Delete device error:', error);
      throw error;
    }
  }

  /**
   * 获取离线设备
   */
  async getOfflineDevices(userId = null, hours = 1) {
    try {
      return await DeviceData.getOfflineDevices(userId, hours);
    } catch (error) {
      logger.error('Get offline devices error:', error);
      throw error;
    }
  }

  /**
   * 设备心跳
   */
  async deviceHeartbeat(deviceId, heartbeatData = {}) {
    try {
      const device = await DeviceData.findOne({ deviceId });
      if (!device) {
        throw new Error('Device not found');
      }

      // 更新最后在线时间
      device.lastOnlineAt = new Date();
      
      // 更新状态为在线
      if (device.status !== 'online') {
        device.status = 'online';
      }

      // 更新统计信息
      if (heartbeatData.uptime) {
        device.stats.uptime = heartbeatData.uptime;
      }
      if (heartbeatData.dataTransfer) {
        Object.assign(device.stats.dataTransfer, heartbeatData.dataTransfer);
      }

      await device.save();
      return device;
    } catch (error) {
      logger.error('Device heartbeat error:', error);
      throw error;
    }
  }

  /**
   * 发送设备命令
   */
  async sendDeviceCommand(deviceId, command, parameters = {}) {
    try {
      const device = await DeviceData.findOne({ deviceId });
      if (!device) {
        throw new Error('Device not found');
      }

      if (device.status !== 'online') {
        throw new Error('Device is offline');
      }

      // TODO: 通过MQTT或WebSocket发送命令到设备
      const commandData = {
        deviceId,
        command,
        parameters,
        timestamp: new Date(),
        id: `cmd_${Date.now()}`
      };

      logger.info(`Command sent to device: ${deviceId} - ${command}`);
      return commandData;
    } catch (error) {
      logger.error('Send device command error:', error);
      throw error;
    }
  }

  /**
   * 获取设备统计信息
   */
  async getDeviceStats(deviceId, userId) {
    try {
      const device = await DeviceData.findOne({ deviceId, userId });
      if (!device) {
        throw new Error('Device not found');
      }

      // 获取传感器数据统计
      const sensorStats = await SensorData.aggregate([
        { $match: { deviceId, userId } },
        {
          $group: {
            _id: '$sensorType',
            count: { $sum: 1 },
            lastReading: { $last: '$timestamp' }
          }
        }
      ]);

      return {
        device: device.stats,
        sensors: sensorStats,
        uptime: device.stats.uptime,
        lastOnline: device.lastOnlineAt,
        status: device.status
      };
    } catch (error) {
      logger.error('Get device stats error:', error);
      throw error;
    }
  }

  /**
   * 重置设备统计
   */
  async resetDeviceStats(deviceId, userId) {
    try {
      const device = await DeviceData.findOne({ deviceId, userId });
      if (!device) {
        throw new Error('Device not found');
      }

      await device.resetStats();
      logger.info(`Device stats reset: ${deviceId}`);

      return device;
    } catch (error) {
      logger.error('Reset device stats error:', error);
      throw error;
    }
  }

  /**
   * 检查设备健康状态
   */
  async checkDeviceHealth(deviceId) {
    try {
      const device = await DeviceData.findOne({ deviceId });
      if (!device) {
        throw new Error('Device not found');
      }

      const health = {
        deviceId,
        status: device.status,
        healthStatus: device.healthStatus,
        lastOnline: device.lastOnlineAt,
        uptime: device.stats.uptime,
        errorCount: device.stats.errorCount,
        issues: []
      };

      // 检查各种健康指标
      if (device.status === 'offline') {
        const offlineTime = Date.now() - device.lastOnlineAt.getTime();
        if (offlineTime > 24 * 60 * 60 * 1000) {
          health.issues.push('Device offline for more than 24 hours');
        }
      }

      if (device.stats.errorCount > 10) {
        health.issues.push('High error count detected');
      }

      if (device.stats.restartCount > 5) {
        health.issues.push('Frequent restarts detected');
      }

      return health;
    } catch (error) {
      logger.error('Check device health error:', error);
      throw error;
    }
  }
}

module.exports = new DeviceService();
