{"pages": ["pages/index/index", "pages/device/device", "pages/data/data", "pages/settings/settings", "pages/alarm/alarm", "pages/light/light", "pages/environment/environment", "pages/sleep/sleep", "pages/config/config", "pages/about/about", "pages/login/login", "pages/device/bind", "pages/device/detail", "pages/monitor/dashboard", "pages/monitor/history", "pages/pet/list", "pages/pet/detail", "pages/pet/create", "pages/task/list", "pages/task/detail", "pages/task/create", "pages/task/pomodoro", "pages/scene/list", "pages/scene/control", "pages/theme/store", "pages/theme/preview", "pages/user/profile"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#2196F3", "navigationBarTitleText": "TIMO智能闹钟", "navigationBarTextStyle": "white", "backgroundColor": "#F5F5F5"}, "tabBar": {"color": "#666666", "selectedColor": "#2196F3", "backgroundColor": "#FFFFFF", "borderStyle": "black", "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "images/icons/home.png", "selectedIconPath": "images/icons/home-active.png"}, {"pagePath": "pages/device/device", "text": "设备", "iconPath": "images/icons/device.png", "selectedIconPath": "images/icons/device-active.png"}, {"pagePath": "pages/data/data", "text": "数据", "iconPath": "images/icons/data.png", "selectedIconPath": "images/icons/data-active.png"}, {"pagePath": "pages/settings/settings", "text": "设置", "iconPath": "images/icons/settings.png", "selectedIconPath": "images/icons/settings-active.png"}]}, "networkTimeout": {"request": 10000, "downloadFile": 10000}, "debug": false, "permission": {"scope.userLocation": {"desc": "用于获取位置信息，提供天气服务"}, "scope.bluetooth": {"desc": "用于连接TIMO设备"}}, "requiredBackgroundModes": ["bluetooth-central"], "plugins": {"echarts": {"version": "1.9.2", "provider": "wx2f1b5e5e5e5e5e5e"}}, "usingComponents": {"van-button": "@vant/weapp/button/index", "van-cell": "@vant/weapp/cell/index", "van-cell-group": "@vant/weapp/cell-group/index", "van-switch": "@vant/weapp/switch/index", "van-slider": "@vant/weapp/slider/index", "van-popup": "@vant/weapp/popup/index", "van-picker": "@vant/weapp/picker/index", "van-datetime-picker": "@vant/weapp/datetime-picker/index", "van-loading": "@vant/weapp/loading/index", "van-toast": "@vant/weapp/toast/index", "van-dialog": "@vant/weapp/dialog/index", "van-notify": "@vant/weapp/notify/index"}, "style": "v2", "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents"}