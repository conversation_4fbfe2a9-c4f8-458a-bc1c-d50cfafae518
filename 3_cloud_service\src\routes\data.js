/**
 * 数据服务路由
 * <AUTHOR> Team
 * @version 1.0.0
 */

const express = require('express');
const router = express.Router();
const authMiddleware = require('../middleware/auth');
const validate = require('../middleware/validate');
const logger = require('../utils/logger');

// 数据模型
const SensorData = require('../models/SensorData');
const DeviceData = require('../models/DeviceData');

/**
 * 上传传感器数据
 * POST /api/data/sensor
 */
router.post('/sensor', authMiddleware, async (req, res) => {
  try {
    const { deviceId, sensorType, value, unit, timestamp } = req.body;
    
    // 验证必需字段
    if (!deviceId || !sensorType || value === undefined) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'deviceId, sensorType, and value are required'
      });
    }

    // 创建传感器数据记录
    const sensorData = new SensorData({
      deviceId,
      sensorType,
      value,
      unit: unit || '',
      timestamp: timestamp || new Date(),
      userId: req.user.id
    });

    await sensorData.save();

    // 实时广播数据更新
    req.app.get('io').emit('data:sensor:update', {
      deviceId,
      sensorType,
      value,
      unit,
      timestamp: sensorData.timestamp
    });

    res.json({
      success: true,
      message: 'Sensor data uploaded successfully',
      data: sensorData
    });

  } catch (error) {
    logger.error('Upload sensor data error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to upload sensor data'
    });
  }
});

/**
 * 批量上传传感器数据
 * POST /api/data/sensor/batch
 */
router.post('/sensor/batch', authMiddleware, async (req, res) => {
  try {
    const { deviceId, data } = req.body;
    
    if (!deviceId || !Array.isArray(data) || data.length === 0) {
      return res.status(400).json({
        error: 'Invalid request',
        message: 'deviceId and data array are required'
      });
    }

    // 批量插入数据
    const sensorDataList = data.map(item => ({
      deviceId,
      sensorType: item.sensorType,
      value: item.value,
      unit: item.unit || '',
      timestamp: item.timestamp || new Date(),
      userId: req.user.id
    }));

    const result = await SensorData.insertMany(sensorDataList);

    res.json({
      success: true,
      message: `${result.length} sensor data records uploaded successfully`,
      count: result.length
    });

  } catch (error) {
    logger.error('Batch upload sensor data error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to batch upload sensor data'
    });
  }
});

/**
 * 获取历史数据
 * GET /api/data/history
 */
router.get('/history', authMiddleware, async (req, res) => {
  try {
    const { 
      deviceId, 
      sensorType, 
      startTime, 
      endTime, 
      limit = 100, 
      page = 1 
    } = req.query;

    // 构建查询条件
    const query = { userId: req.user.id };
    if (deviceId) query.deviceId = deviceId;
    if (sensorType) query.sensorType = sensorType;
    
    if (startTime || endTime) {
      query.timestamp = {};
      if (startTime) query.timestamp.$gte = new Date(startTime);
      if (endTime) query.timestamp.$lte = new Date(endTime);
    }

    // 分页查询
    const skip = (page - 1) * limit;
    const data = await SensorData.find(query)
      .sort({ timestamp: -1 })
      .limit(parseInt(limit))
      .skip(skip);

    const total = await SensorData.countDocuments(query);

    res.json({
      success: true,
      data,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    logger.error('Get history data error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get history data'
    });
  }
});

/**
 * 获取统计数据
 * GET /api/data/statistics
 */
router.get('/statistics', authMiddleware, async (req, res) => {
  try {
    const { deviceId, sensorType, period = 'day' } = req.query;

    // 构建查询条件
    const query = { userId: req.user.id };
    if (deviceId) query.deviceId = deviceId;
    if (sensorType) query.sensorType = sensorType;

    // 计算时间范围
    const now = new Date();
    let startTime;
    switch (period) {
      case 'hour':
        startTime = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case 'day':
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'week':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    query.timestamp = { $gte: startTime };

    // 聚合统计
    const stats = await SensorData.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$sensorType',
          count: { $sum: 1 },
          avgValue: { $avg: '$value' },
          minValue: { $min: '$value' },
          maxValue: { $max: '$value' },
          lastValue: { $last: '$value' },
          lastTimestamp: { $last: '$timestamp' }
        }
      }
    ]);

    res.json({
      success: true,
      period,
      startTime,
      endTime: now,
      statistics: stats
    });

  } catch (error) {
    logger.error('Get statistics error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get statistics'
    });
  }
});

/**
 * 获取实时数据
 * GET /api/data/realtime
 */
router.get('/realtime', authMiddleware, async (req, res) => {
  try {
    const { deviceId } = req.query;

    if (!deviceId) {
      return res.status(400).json({
        error: 'Missing parameter',
        message: 'deviceId is required'
      });
    }

    // 获取最新的传感器数据
    const latestData = await SensorData.aggregate([
      {
        $match: {
          deviceId,
          userId: req.user.id,
          timestamp: { $gte: new Date(Date.now() - 5 * 60 * 1000) } // 最近5分钟
        }
      },
      {
        $sort: { timestamp: -1 }
      },
      {
        $group: {
          _id: '$sensorType',
          value: { $first: '$value' },
          unit: { $first: '$unit' },
          timestamp: { $first: '$timestamp' }
        }
      }
    ]);

    res.json({
      success: true,
      deviceId,
      data: latestData,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get realtime data error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get realtime data'
    });
  }
});

/**
 * 删除历史数据
 * DELETE /api/data/history
 */
router.delete('/history', authMiddleware, async (req, res) => {
  try {
    const { deviceId, beforeDate } = req.body;

    const query = { userId: req.user.id };
    if (deviceId) query.deviceId = deviceId;
    if (beforeDate) query.timestamp = { $lt: new Date(beforeDate) };

    const result = await SensorData.deleteMany(query);

    res.json({
      success: true,
      message: `${result.deletedCount} records deleted successfully`,
      deletedCount: result.deletedCount
    });

  } catch (error) {
    logger.error('Delete history data error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to delete history data'
    });
  }
});

module.exports = router;
