/**
 * MQTT服务路由
 * <AUTHOR> Team
 * @version 1.0.0
 */

const express = require('express');
const router = express.Router();
const authMiddleware = require('../middleware/auth');
const mqttService = require('../services/mqttService');
const logger = require('../utils/logger');

/**
 * 获取MQTT服务状态
 * GET /api/mqtt/status
 */
router.get('/status', authMiddleware, async (req, res) => {
  try {
    const status = mqttService.getStatus();
    
    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    logger.error('Get MQTT status error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get MQTT status'
    });
  }
});

/**
 * 发送命令到设备
 * POST /api/mqtt/device/:deviceId/command
 */
router.post('/device/:deviceId/command', authMiddleware, async (req, res) => {
  try {
    const { deviceId } = req.params;
    const { command, parameters = {} } = req.body;

    if (!command) {
      return res.status(400).json({
        error: 'Missing command',
        message: 'Command is required'
      });
    }

    // 验证设备权限
    // TODO: 检查用户是否有权限控制该设备

    // 发送命令到设备
    mqttService.publishToDevice(deviceId, 'command', command, {
      parameters,
      userId: req.user.id,
      requestId: `cmd_${Date.now()}`
    });

    res.json({
      success: true,
      message: 'Command sent successfully',
      data: {
        deviceId,
        command,
        parameters,
        timestamp: new Date()
      }
    });

  } catch (error) {
    logger.error('Send device command error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to send device command'
    });
  }
});

/**
 * 更新设备配置
 * POST /api/mqtt/device/:deviceId/config
 */
router.post('/device/:deviceId/config', authMiddleware, async (req, res) => {
  try {
    const { deviceId } = req.params;
    const { configPath, value } = req.body;

    if (!configPath || value === undefined) {
      return res.status(400).json({
        error: 'Missing parameters',
        message: 'configPath and value are required'
      });
    }

    // 发送配置更新到设备
    mqttService.publishToDevice(deviceId, 'config', 'update', {
      configPath,
      value,
      userId: req.user.id,
      requestId: `cfg_${Date.now()}`
    });

    res.json({
      success: true,
      message: 'Config update sent successfully',
      data: {
        deviceId,
        configPath,
        value,
        timestamp: new Date()
      }
    });

  } catch (error) {
    logger.error('Update device config error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to update device config'
    });
  }
});

/**
 * 发送语音合成请求
 * POST /api/mqtt/device/:deviceId/tts
 */
router.post('/device/:deviceId/tts', authMiddleware, async (req, res) => {
  try {
    const { deviceId } = req.params;
    const { text, voice = 'default', speed = 1.0, pitch = 1.0 } = req.body;

    if (!text) {
      return res.status(400).json({
        error: 'Missing text',
        message: 'Text is required for TTS'
      });
    }

    // 发送TTS请求到设备
    mqttService.publishToDevice(deviceId, 'voice', 'tts', {
      text,
      voice,
      speed,
      pitch,
      userId: req.user.id,
      requestId: `tts_${Date.now()}`
    });

    res.json({
      success: true,
      message: 'TTS request sent successfully',
      data: {
        deviceId,
        text,
        voice,
        speed,
        pitch,
        timestamp: new Date()
      }
    });

  } catch (error) {
    logger.error('Send TTS request error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to send TTS request'
    });
  }
});

/**
 * 发送任务到设备
 * POST /api/mqtt/device/:deviceId/task
 */
router.post('/device/:deviceId/task', authMiddleware, async (req, res) => {
  try {
    const { deviceId } = req.params;
    const { action, taskData } = req.body;

    if (!action) {
      return res.status(400).json({
        error: 'Missing action',
        message: 'Action is required'
      });
    }

    // 发送任务到设备
    mqttService.publishToDevice(deviceId, 'task', action, {
      taskData,
      userId: req.user.id,
      requestId: `task_${Date.now()}`
    });

    res.json({
      success: true,
      message: 'Task sent successfully',
      data: {
        deviceId,
        action,
        taskData,
        timestamp: new Date()
      }
    });

  } catch (error) {
    logger.error('Send task error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to send task'
    });
  }
});

/**
 * 发送场景切换命令
 * POST /api/mqtt/device/:deviceId/scene
 */
router.post('/device/:deviceId/scene', authMiddleware, async (req, res) => {
  try {
    const { deviceId } = req.params;
    const { sceneId, parameters = {} } = req.body;

    if (!sceneId) {
      return res.status(400).json({
        error: 'Missing sceneId',
        message: 'Scene ID is required'
      });
    }

    // 发送场景切换命令到设备
    mqttService.publishToDevice(deviceId, 'command', 'switch_scene', {
      sceneId,
      parameters,
      userId: req.user.id,
      requestId: `scene_${Date.now()}`
    });

    res.json({
      success: true,
      message: 'Scene switch command sent successfully',
      data: {
        deviceId,
        sceneId,
        parameters,
        timestamp: new Date()
      }
    });

  } catch (error) {
    logger.error('Send scene switch command error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to send scene switch command'
    });
  }
});

/**
 * 发送闹钟设置命令
 * POST /api/mqtt/device/:deviceId/alarm
 */
router.post('/device/:deviceId/alarm', authMiddleware, async (req, res) => {
  try {
    const { deviceId } = req.params;
    const { action, alarmData } = req.body;

    if (!action) {
      return res.status(400).json({
        error: 'Missing action',
        message: 'Action is required (create, update, delete, enable, disable)'
      });
    }

    // 发送闹钟命令到设备
    mqttService.publishToDevice(deviceId, 'command', `alarm_${action}`, {
      alarmData,
      userId: req.user.id,
      requestId: `alarm_${Date.now()}`
    });

    res.json({
      success: true,
      message: 'Alarm command sent successfully',
      data: {
        deviceId,
        action,
        alarmData,
        timestamp: new Date()
      }
    });

  } catch (error) {
    logger.error('Send alarm command error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to send alarm command'
    });
  }
});

/**
 * 获取设备实时事件流
 * GET /api/mqtt/device/:deviceId/events
 */
router.get('/device/:deviceId/events', authMiddleware, async (req, res) => {
  try {
    const { deviceId } = req.params;

    // 设置SSE响应头
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // 发送初始连接消息
    res.write(`data: ${JSON.stringify({
      type: 'connected',
      deviceId,
      timestamp: new Date()
    })}\n\n`);

    // 监听MQTT事件
    const eventHandlers = {
      sensorData: (data) => {
        if (data.deviceId === deviceId) {
          res.write(`data: ${JSON.stringify({
            type: 'sensorData',
            ...data
          })}\n\n`);
        }
      },
      deviceStatus: (data) => {
        if (data.deviceId === deviceId) {
          res.write(`data: ${JSON.stringify({
            type: 'deviceStatus',
            ...data
          })}\n\n`);
        }
      },
      deviceHeartbeat: (data) => {
        if (data.deviceId === deviceId) {
          res.write(`data: ${JSON.stringify({
            type: 'heartbeat',
            ...data
          })}\n\n`);
        }
      }
    };

    // 注册事件监听器
    Object.entries(eventHandlers).forEach(([event, handler]) => {
      mqttService.on(event, handler);
    });

    // 客户端断开连接时清理
    req.on('close', () => {
      Object.entries(eventHandlers).forEach(([event, handler]) => {
        mqttService.removeListener(event, handler);
      });
    });

  } catch (error) {
    logger.error('Setup device events stream error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to setup events stream'
    });
  }
});

/**
 * 广播消息到所有设备
 * POST /api/mqtt/broadcast
 */
router.post('/broadcast', authMiddleware, async (req, res) => {
  try {
    const { type, command, payload = {} } = req.body;

    if (!type || !command) {
      return res.status(400).json({
        error: 'Missing parameters',
        message: 'Type and command are required'
      });
    }

    // TODO: 检查管理员权限

    // 广播到所有设备
    mqttService.publishToDevice('+', type, command, {
      ...payload,
      userId: req.user.id,
      requestId: `broadcast_${Date.now()}`
    });

    res.json({
      success: true,
      message: 'Broadcast message sent successfully',
      data: {
        type,
        command,
        payload,
        timestamp: new Date()
      }
    });

  } catch (error) {
    logger.error('Send broadcast message error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to send broadcast message'
    });
  }
});

module.exports = router;
