/**
 * @file network_manager.c
 * @brief TIMO网络管理器实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "network_manager.h"
#include "bluetooth_system.h"
#include "bt_scene_sync.h"
#include "esp_log.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_netif.h"
#include "nvs_flash.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include <string.h>

static const char *TAG = "NETWORK_MANAGER";

static bool g_wifi_connected = false;
static bool g_bluetooth_connected = false;
static int8_t g_wifi_rssi = -100;
static bool g_bluetooth_initialized = false;
static bool g_wifi_initialized = false;
static esp_netif_t *g_sta_netif = NULL;

/* WiFi事件组 */
#define WIFI_CONNECTED_BIT BIT0
#define WIFI_FAIL_BIT      BIT1
static EventGroupHandle_t g_wifi_event_group;

/**
 * @brief WiFi事件处理器
 */
static void wifi_event_handler(void* arg, esp_event_base_t event_base,
                              int32_t event_id, void* event_data)
{
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        ESP_LOGI(TAG, "WiFi STA启动");
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        ESP_LOGI(TAG, "WiFi连接断开，尝试重连...");
        g_wifi_connected = false;
        esp_wifi_connect();
        xEventGroupClearBits(g_wifi_event_group, WIFI_CONNECTED_BIT);
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "获得IP地址:" IPSTR, IP2STR(&event->ip_info.ip));
        g_wifi_connected = true;
        xEventGroupSetBits(g_wifi_event_group, WIFI_CONNECTED_BIT);
    }
}

/**
 * @brief 蓝牙事件回调
 */
static void bluetooth_event_callback(bt_event_type_t event, void *data)
{
    switch (event) {
        case BT_EVENT_CONNECTED:
            g_bluetooth_connected = true;
            ESP_LOGI(TAG, "蓝牙已连接到底座设备");
            break;

        case BT_EVENT_DISCONNECTED:
            g_bluetooth_connected = false;
            ESP_LOGI(TAG, "蓝牙与底座设备断开连接");
            break;

        case BT_EVENT_SCAN_COMPLETE:
            ESP_LOGI(TAG, "蓝牙扫描完成");
            break;

        default:
            break;
    }
}

esp_err_t network_manager_init(void)
{
    ESP_LOGI(TAG, "初始化网络管理器...");

    // 创建WiFi事件组
    g_wifi_event_group = xEventGroupCreate();
    if (g_wifi_event_group == NULL) {
        ESP_LOGE(TAG, "创建WiFi事件组失败");
        return ESP_ERR_NO_MEM;
    }

    // 创建默认网络接口
    g_sta_netif = esp_netif_create_default_wifi_sta();
    if (g_sta_netif == NULL) {
        ESP_LOGE(TAG, "创建WiFi STA网络接口失败");
        return ESP_ERR_NO_MEM;
    }

    // 初始化蓝牙系统
    esp_err_t ret = bluetooth_system_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "蓝牙系统初始化失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 初始化场景同步模块
    ret = bt_scene_sync_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "场景同步模块初始化失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 注册蓝牙事件回调
    bluetooth_system_register_event_callback(bluetooth_event_callback);

    g_bluetooth_initialized = true;
    ESP_LOGI(TAG, "网络管理器初始化完成");
    return ESP_OK;
}

esp_err_t network_manager_start_wifi(void)
{
    ESP_LOGI(TAG, "启动WiFi...");

    // 初始化WiFi
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    esp_err_t ret = esp_wifi_init(&cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "WiFi初始化失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 注册WiFi事件处理器
    ret = esp_event_handler_register(WIFI_EVENT, ESP_EVENT_ANY_ID, &wifi_event_handler, NULL);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "注册WiFi事件处理器失败: %s", esp_err_to_name(ret));
        return ret;
    }

    ret = esp_event_handler_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &wifi_event_handler, NULL);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "注册IP事件处理器失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 设置WiFi模式为STA
    ret = esp_wifi_set_mode(WIFI_MODE_STA);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "设置WiFi模式失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 启动WiFi
    ret = esp_wifi_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "启动WiFi失败: %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "WiFi启动完成");
    return ESP_OK;
}

esp_err_t network_manager_start_bluetooth(void)
{
    if (!g_bluetooth_initialized) {
        ESP_LOGE(TAG, "蓝牙系统未初始化");
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "启动蓝牙系统...");

    // 启动蓝牙系统
    esp_err_t ret = bluetooth_system_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "蓝牙系统启动失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 开始扫描底座设备
    ret = bluetooth_system_start_scan(30); // 30秒超时
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "蓝牙扫描启动失败: %s", esp_err_to_name(ret));
    }

    ESP_LOGI(TAG, "蓝牙系统启动完成");
    return ESP_OK;
}

esp_err_t network_manager_stop(void)
{
    ESP_LOGI(TAG, "停止网络管理器...");

    if (g_bluetooth_initialized) {
        // 停止蓝牙系统
        bluetooth_system_stop();
        bt_scene_sync_deinit();
        bluetooth_system_deinit();
        g_bluetooth_initialized = false;
    }

    g_bluetooth_connected = false;
    return ESP_OK;
}

bool network_manager_is_wifi_connected(void)
{
    return g_wifi_connected;
}

bool network_manager_is_bluetooth_connected(void)
{
    return g_bluetooth_connected;
}

int8_t network_manager_get_wifi_rssi(void)
{
    return g_wifi_rssi;
}

/**
 * @brief 连接到指定的底座设备
 */
esp_err_t network_manager_connect_to_base(const esp_bd_addr_t device_addr)
{
    if (!g_bluetooth_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "连接到底座设备");
    return bluetooth_system_connect(device_addr);
}

/**
 * @brief 断开与底座设备的连接
 */
esp_err_t network_manager_disconnect_from_base(void)
{
    if (!g_bluetooth_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "断开与底座设备的连接");
    return bluetooth_system_disconnect();
}

/**
 * @brief 连接到WiFi网络
 */
esp_err_t network_manager_connect_wifi(const char *ssid, const char *password)
{
    if (!g_wifi_initialized) {
        ESP_LOGE(TAG, "WiFi未初始化");
        return ESP_ERR_INVALID_STATE;
    }

    if (!ssid) {
        ESP_LOGE(TAG, "SSID不能为空");
        return ESP_ERR_INVALID_ARG;
    }

    ESP_LOGI(TAG, "连接到WiFi: %s", ssid);

    wifi_config_t wifi_config = {
        .sta = {
            .threshold.authmode = WIFI_AUTH_WPA2_PSK,
            .pmf_cfg = {
                .capable = true,
                .required = false
            },
        },
    };

    // 设置SSID
    strncpy((char *)wifi_config.sta.ssid, ssid, sizeof(wifi_config.sta.ssid) - 1);
    wifi_config.sta.ssid[sizeof(wifi_config.sta.ssid) - 1] = '\0';

    // 设置密码
    if (password && strlen(password) > 0) {
        strncpy((char *)wifi_config.sta.password, password, sizeof(wifi_config.sta.password) - 1);
        wifi_config.sta.password[sizeof(wifi_config.sta.password) - 1] = '\0';
    }

    esp_err_t ret = esp_wifi_set_config(WIFI_IF_STA, &wifi_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "设置WiFi配置失败: %s", esp_err_to_name(ret));
        return ret;
    }

    ret = esp_wifi_connect();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "连接WiFi失败: %s", esp_err_to_name(ret));
        return ret;
    }

    return ESP_OK;
}

/**
 * @brief 扫描WiFi网络
 */
esp_err_t network_manager_scan_wifi(wifi_ap_record_t *ap_records, uint16_t *ap_count)
{
    if (!g_wifi_initialized) {
        ESP_LOGE(TAG, "WiFi未初始化");
        return ESP_ERR_INVALID_STATE;
    }

    if (!ap_records || !ap_count) {
        ESP_LOGE(TAG, "参数不能为空");
        return ESP_ERR_INVALID_ARG;
    }

    ESP_LOGI(TAG, "开始扫描WiFi网络...");

    wifi_scan_config_t scan_config = {
        .ssid = NULL,
        .bssid = NULL,
        .channel = 0,
        .show_hidden = false,
        .scan_type = WIFI_SCAN_TYPE_ACTIVE,
        .scan_time = {
            .active = {
                .min = 100,
                .max = 300
            }
        }
    };

    esp_err_t ret = esp_wifi_scan_start(&scan_config, true);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "启动WiFi扫描失败: %s", esp_err_to_name(ret));
        return ret;
    }

    ret = esp_wifi_scan_get_ap_records(ap_count, ap_records);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "获取扫描结果失败: %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "扫描到 %d 个WiFi网络", *ap_count);
    return ESP_OK;
}

/**
 * @brief 断开WiFi连接
 */
esp_err_t network_manager_disconnect_wifi(void)
{
    if (!g_wifi_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "断开WiFi连接");
    g_wifi_connected = false;
    return esp_wifi_disconnect();
}

/**
 * @brief 获取扫描到的底座设备列表
 */
uint8_t network_manager_get_base_devices(bt_device_info_t *devices, uint8_t max_count)
{
    if (!g_bluetooth_initialized) {
        return 0;
    }

    return bluetooth_system_get_scan_results(devices, max_count);
}

/**
 * @brief 重新扫描底座设备
 */
esp_err_t network_manager_rescan_base_devices(void)
{
    if (!g_bluetooth_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "重新扫描底座设备");
    return bluetooth_system_start_scan(30);
}

/**
 * @brief 获取蓝牙连接状态
 */
bt_connection_state_t network_manager_get_bluetooth_state(void)
{
    if (!g_bluetooth_initialized) {
        return BT_STATE_IDLE;
    }

    return bluetooth_system_get_state();
}
