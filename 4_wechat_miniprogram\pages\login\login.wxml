<!--登录页面-->
<view class="container">
  <!-- 背景装饰 -->
  <view class="bg-decoration">
    <view class="circle circle-1"></view>
    <view class="circle circle-2"></view>
    <view class="circle circle-3"></view>
  </view>

  <!-- 欢迎界面 -->
  <view wx:if="{{loginStep === 'welcome'}}" class="welcome-content">
    <!-- 应用Logo -->
    <view class="app-logo">
      <image class="logo-image" src="/images/logo.png" mode="aspectFit"></image>
    </view>
    
    <!-- 应用信息 -->
    <view class="app-info">
      <text class="app-name">{{appInfo.name}}</text>
      <text class="app-description">{{appInfo.description}}</text>
      <text class="app-version">v{{appInfo.version}}</text>
    </view>
    
    <!-- 登录按钮 -->
    <view class="login-actions">
      <button class="login-btn primary" open-type="getUserInfo" bindgetuserinfo="onGetUserInfo">
        <image class="btn-icon" src="/images/icons/wechat.png" mode="aspectFit"></image>
        微信快速登录
      </button>
      
      <button class="login-btn secondary" bindtap="onSkipLogin">
        <text>游客模式</text>
      </button>
    </view>
    
    <!-- 协议条款 -->
    <view class="agreement">
      <text class="agreement-text">登录即表示同意</text>
      <text class="agreement-link" bindtap="onTermsTap">《用户协议》</text>
      <text class="agreement-text">和</text>
      <text class="agreement-link" bindtap="onPrivacyTap">《隐私政策》</text>
    </view>
  </view>

  <!-- 获取用户信息界面 -->
  <view wx:elif="{{loginStep === 'getUserInfo'}}" class="userinfo-content">
    <view class="userinfo-card">
      <view class="card-header">
        <text class="card-title">完善个人信息</text>
        <text class="card-subtitle">为您提供更好的服务体验</text>
      </view>
      
      <view class="userinfo-form">
        <view class="form-item">
          <text class="form-label">头像和昵称</text>
          <button class="userinfo-btn" open-type="getUserInfo" bindgetuserinfo="onGetUserInfo">
            <image wx:if="{{userInfo.avatarUrl}}" class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
            <view wx:else class="avatar-placeholder">
              <text class="placeholder-text">点击获取</text>
            </view>
            <text class="user-nickname">{{userInfo.nickName || '点击获取昵称'}}</text>
          </button>
        </view>
      </view>
      
      <view class="form-actions">
        <button wx:if="{{hasUserInfo}}" class="action-btn primary" bindtap="onWxLogin" loading="{{loading}}">
          开始使用
        </button>
        <button wx:else class="action-btn disabled" disabled>
          请先获取用户信息
        </button>
      </view>
    </view>
  </view>

  <!-- 登录界面 -->
  <view wx:elif="{{loginStep === 'login'}}" class="login-content">
    <view class="login-card">
      <view class="user-preview">
        <image class="preview-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
        <text class="preview-name">{{userInfo.nickName}}</text>
        <text class="preview-desc">欢迎使用TIMO智能闹钟</text>
      </view>
      
      <view class="login-form">
        <button class="login-submit-btn" bindtap="onWxLogin" loading="{{loading}}">
          <image wx:if="{{!loading}}" class="btn-icon" src="/images/icons/wechat.png" mode="aspectFit"></image>
          {{loading ? '登录中...' : '微信登录'}}
        </button>
        
        <view class="login-tips">
          <text class="tips-text">• 安全快速，无需注册</text>
          <text class="tips-text">• 数据云端同步，永不丢失</text>
          <text class="tips-text">• 多设备管理，随时随地</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 登录成功界面 -->
  <view wx:elif="{{loginStep === 'success'}}" class="success-content">
    <view class="success-animation">
      <view class="success-icon">
        <text class="check-mark">✓</text>
      </view>
      <text class="success-text">登录成功</text>
      <text class="success-desc">正在为您准备个性化体验...</text>
    </view>
    
    <view class="loading-progress">
      <view class="progress-bar">
        <view class="progress-fill"></view>
      </view>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer">
    <view class="footer-links">
      <text class="footer-link" bindtap="onContactTap">联系客服</text>
      <text class="footer-divider">|</text>
      <text class="footer-link" bindtap="onAboutTap">关于我们</text>
    </view>
    <text class="footer-copyright">© 2025 TIMO Team. All rights reserved.</text>
  </view>
</view>
