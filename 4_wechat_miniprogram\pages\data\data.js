/**
 * 数据展示页面
 * <AUTHOR> Team
 * @version 1.0.0
 */

const app = getApp();
const api = require('../../utils/api');

Page({
  data: {
    // 当前设备
    currentDevice: null,
    
    // 时间范围
    timeRange: 'day', // hour, day, week, month
    timeRanges: [
      { value: 'hour', label: '最近1小时' },
      { value: 'day', label: '最近24小时' },
      { value: 'week', label: '最近7天' },
      { value: 'month', label: '最近30天' }
    ],
    
    // 传感器类型
    sensorType: 'temperature',
    sensorTypes: [
      { value: 'temperature', label: '温度', unit: '°C', color: '#FF6B6B' },
      { value: 'humidity', label: '湿度', unit: '%', color: '#4ECDC4' },
      { value: 'co2', label: 'CO2', unit: 'ppm', color: '#45B7D1' },
      { value: 'light', label: '光照', unit: 'lux', color: '#FFA726' }
    ],
    
    // 图表数据
    chartData: [],
    chartOptions: {},
    
    // 统计数据
    statistics: {
      current: '--',
      avg: '--',
      min: '--',
      max: '--'
    },
    
    // 实时数据
    realtimeData: [],
    
    // 页面状态
    loading: true,
    refreshing: false,
    
    // 图表组件
    ec: null,
    chart: null
  },

  /**
   * 页面加载
   */
  onLoad() {
    console.log('数据页面加载');
    this.initPage();
  },

  /**
   * 页面显示
   */
  onShow() {
    console.log('数据页面显示');
    this.refreshData();
  },

  /**
   * 页面准备就绪
   */
  onReady() {
    // 初始化图表组件
    this.initChart();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshData(true);
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 检查登录状态
      if (!app.globalData.token) {
        wx.reLaunch({
          url: '/pages/login/login'
        });
        return;
      }

      // 设置当前设备
      this.setData({
        currentDevice: app.globalData.currentDevice
      });

      if (!this.data.currentDevice) {
        app.showError('请先选择设备');
        wx.switchTab({
          url: '/pages/device/device'
        });
        return;
      }

      // 加载数据
      await this.loadData();
    } catch (error) {
      console.error('初始化数据页面失败:', error);
      app.showError('页面加载失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 刷新数据
   */
  async refreshData(isPullRefresh = false) {
    if (isPullRefresh) {
      this.setData({ refreshing: true });
    }

    try {
      await this.loadData();
    } catch (error) {
      console.error('刷新数据失败:', error);
      app.showError('刷新失败');
    } finally {
      if (isPullRefresh) {
        this.setData({ refreshing: false });
        wx.stopPullDownRefresh();
      }
    }
  },

  /**
   * 加载数据
   */
  async loadData() {
    try {
      await Promise.all([
        this.loadChartData(),
        this.loadStatistics(),
        this.loadRealtimeData()
      ]);
    } catch (error) {
      console.error('加载数据失败:', error);
      throw error;
    }
  },

  /**
   * 加载图表数据
   */
  async loadChartData() {
    try {
      const data = await api.getTrendData(
        this.data.currentDevice.deviceId,
        this.data.sensorType,
        this.data.timeRange
      );

      const chartData = this.formatChartData(data);
      this.setData({ chartData });

      // 更新图表
      if (this.data.chart) {
        this.updateChart(chartData);
      }

      console.log('图表数据:', chartData);
    } catch (error) {
      console.error('加载图表数据失败:', error);
      throw error;
    }
  },

  /**
   * 加载统计数据
   */
  async loadStatistics() {
    try {
      const stats = await api.getStatistics(
        this.data.currentDevice.deviceId,
        this.data.sensorType,
        this.data.timeRange
      );

      const currentSensor = this.data.sensorTypes.find(s => s.value === this.data.sensorType);
      const unit = currentSensor ? currentSensor.unit : '';

      this.setData({
        statistics: {
          current: stats.current ? `${stats.current}${unit}` : '--',
          avg: stats.avg ? `${stats.avg.toFixed(1)}${unit}` : '--',
          min: stats.min ? `${stats.min}${unit}` : '--',
          max: stats.max ? `${stats.max}${unit}` : '--'
        }
      });

      console.log('统计数据:', stats);
    } catch (error) {
      console.error('加载统计数据失败:', error);
      // 不抛出错误，使用默认值
    }
  },

  /**
   * 加载实时数据
   */
  async loadRealtimeData() {
    try {
      const data = await api.getRealtimeData(this.data.currentDevice.deviceId);
      this.setData({ realtimeData: data });

      console.log('实时数据:', data);
    } catch (error) {
      console.error('加载实时数据失败:', error);
      // 不抛出错误，使用默认值
    }
  },

  /**
   * 格式化图表数据
   */
  formatChartData(data) {
    if (!data || data.length === 0) {
      return { xAxis: [], series: [] };
    }

    const xAxis = data.map(item => this.formatTimeLabel(item.timestamp));
    const series = data.map(item => item.avg || item.value || 0);

    return { xAxis, series };
  },

  /**
   * 格式化时间标签
   */
  formatTimeLabel(timestamp) {
    const date = new Date(timestamp);
    
    switch (this.data.timeRange) {
      case 'hour':
        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
      case 'day':
        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
      case 'week':
        return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
      case 'month':
        return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
      default:
        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    }
  },

  /**
   * 初始化图表
   */
  initChart() {
    this.selectComponent('#chart').init((canvas, width, height, dpr) => {
      const chart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr
      });
      
      this.setData({ chart });
      
      // 设置初始配置
      this.updateChart(this.data.chartData);
      
      return chart;
    });
  },

  /**
   * 更新图表
   */
  updateChart(data) {
    if (!this.data.chart || !data) return;

    const currentSensor = this.data.sensorTypes.find(s => s.value === this.data.sensorType);
    const color = currentSensor ? currentSensor.color : '#4ECDC4';
    const unit = currentSensor ? currentSensor.unit : '';

    const option = {
      grid: {
        left: '10%',
        right: '10%',
        top: '15%',
        bottom: '15%'
      },
      xAxis: {
        type: 'category',
        data: data.xAxis || [],
        axisLabel: {
          fontSize: 10,
          color: '#666'
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: 10,
          color: '#666',
          formatter: `{value}${unit}`
        }
      },
      series: [{
        type: 'line',
        data: data.series || [],
        smooth: true,
        lineStyle: {
          color: color,
          width: 2
        },
        itemStyle: {
          color: color
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: color + '40'
            }, {
              offset: 1,
              color: color + '10'
            }]
          }
        }
      }],
      tooltip: {
        trigger: 'axis',
        formatter: `{b}<br/>{a}: {c}${unit}`
      }
    };

    this.data.chart.setOption(option);
  },

  /**
   * 时间范围选择
   */
  onTimeRangeChange(e) {
    const timeRange = e.detail.value;
    const selectedRange = this.data.timeRanges[timeRange];
    
    this.setData({
      timeRange: selectedRange.value
    });

    this.loadData();
  },

  /**
   * 传感器类型选择
   */
  onSensorTypeChange(e) {
    const sensorType = e.detail.value;
    const selectedSensor = this.data.sensorTypes[sensorType];
    
    this.setData({
      sensorType: selectedSensor.value
    });

    this.loadData();
  },

  /**
   * 实时数据项点击
   */
  onRealtimeDataTap(e) {
    const { sensor } = e.currentTarget.dataset;
    
    this.setData({
      sensorType: sensor._id
    });

    this.loadData();
  },

  /**
   * 导出数据
   */
  async onExportData() {
    try {
      app.showLoading('导出数据中...');
      
      const exportData = await api.exportData(
        this.data.currentDevice.deviceId,
        this.data.sensorType,
        this.data.timeRange
      );

      // 这里可以实现数据导出功能
      // 比如生成CSV文件或调用分享接口
      
      app.showSuccess('数据导出成功');
    } catch (error) {
      console.error('导出数据失败:', error);
      app.showError('导出失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 查看历史详情
   */
  onViewHistory() {
    wx.navigateTo({
      url: `/pages/monitor/history?deviceId=${this.data.currentDevice.deviceId}&sensorType=${this.data.sensorType}`
    });
  }
});
