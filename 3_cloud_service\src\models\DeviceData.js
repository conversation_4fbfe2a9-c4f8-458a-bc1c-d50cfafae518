/**
 * 设备数据模型
 * <AUTHOR> Team
 * @version 1.0.0
 */

const mongoose = require('mongoose');

const deviceDataSchema = new mongoose.Schema({
  // 设备ID
  deviceId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  // 用户ID
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  // 设备名称
  name: {
    type: String,
    required: true,
    maxlength: 100
  },
  
  // 设备类型
  type: {
    type: String,
    required: true,
    enum: ['main_device', 'base_station'],
    index: true
  },
  
  // 设备型号
  model: {
    type: String,
    required: true
  },
  
  // 硬件版本
  hardwareVersion: {
    type: String,
    required: true
  },
  
  // 固件版本
  firmwareVersion: {
    type: String,
    required: true
  },
  
  // 设备状态
  status: {
    type: String,
    enum: ['online', 'offline', 'sleeping', 'error'],
    default: 'offline',
    index: true
  },
  
  // 最后在线时间
  lastOnlineAt: {
    type: Date,
    default: Date.now
  },
  
  // 最后离线时间
  lastOfflineAt: {
    type: Date
  },
  
  // 设备配置
  config: {
    // 显示设置
    display: {
      brightness: { type: Number, min: 0, max: 100, default: 80 },
      theme: { type: String, default: 'default' },
      autoSleep: { type: Boolean, default: true },
      sleepTimeout: { type: Number, default: 300 } // 秒
    },
    
    // 音频设置
    audio: {
      volume: { type: Number, min: 0, max: 100, default: 50 },
      mute: { type: Boolean, default: false },
      voiceEngine: { type: String, enum: ['local', 'esp-sr', 'cloud', 'hybrid'], default: 'local' }
    },
    
    // 传感器设置
    sensors: {
      temperature: {
        enabled: { type: Boolean, default: true },
        interval: { type: Number, default: 60 }, // 秒
        threshold: { min: { type: Number, default: 16 }, max: { type: Number, default: 30 } }
      },
      humidity: {
        enabled: { type: Boolean, default: true },
        interval: { type: Number, default: 60 },
        threshold: { min: { type: Number, default: 30 }, max: { type: Number, default: 70 } }
      },
      co2: {
        enabled: { type: Boolean, default: true },
        interval: { type: Number, default: 300 },
        threshold: { min: { type: Number, default: 400 }, max: { type: Number, default: 1000 } }
      },
      light: {
        enabled: { type: Boolean, default: true },
        interval: { type: Number, default: 60 },
        autoBrightness: { type: Boolean, default: true }
      }
    },
    
    // 网络设置
    network: {
      wifi: {
        ssid: String,
        connected: { type: Boolean, default: false },
        signalStrength: Number
      },
      bluetooth: {
        enabled: { type: Boolean, default: true },
        paired: { type: Boolean, default: false },
        pairedDevice: String
      }
    },
    
    // 闹钟设置
    alarms: [{
      id: String,
      name: String,
      time: String, // HH:MM格式
      days: [Number], // 0-6，0为周日
      enabled: { type: Boolean, default: true },
      sound: String,
      volume: Number,
      scene: String
    }],
    
    // 氛围场景设置
    scenes: {
      current: { type: String, default: 'standby' },
      brightness: { type: Number, min: 0, max: 100, default: 50 },
      color: { type: String, default: '#FFFFFF' },
      effect: { type: String, default: 'static' }
    }
  },
  
  // 设备统计信息
  stats: {
    // 运行时间（秒）
    uptime: { type: Number, default: 0 },
    
    // 重启次数
    restartCount: { type: Number, default: 0 },
    
    // 错误次数
    errorCount: { type: Number, default: 0 },
    
    // 数据传输统计
    dataTransfer: {
      sent: { type: Number, default: 0 }, // 字节
      received: { type: Number, default: 0 } // 字节
    },
    
    // 语音交互统计
    voice: {
      wakeupCount: { type: Number, default: 0 },
      commandCount: { type: Number, default: 0 },
      successRate: { type: Number, default: 0 }
    },
    
    // 传感器数据统计
    sensorReadings: { type: Number, default: 0 }
  },
  
  // 设备位置信息
  location: {
    room: String,
    floor: String,
    building: String,
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  
  // 设备标签
  tags: [{
    type: String,
    maxlength: 50
  }],
  
  // 备注
  notes: {
    type: String,
    maxlength: 1000
  },
  
  // 最后更新时间
  lastUpdatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  collection: 'device_data'
});

// 索引
deviceDataSchema.index({ userId: 1, type: 1 });
deviceDataSchema.index({ status: 1, lastOnlineAt: -1 });

// 虚拟字段：在线时长
deviceDataSchema.virtual('onlineDuration').get(function() {
  if (this.status === 'online' && this.lastOnlineAt) {
    return Date.now() - this.lastOnlineAt.getTime();
  }
  return 0;
});

// 虚拟字段：离线时长
deviceDataSchema.virtual('offlineDuration').get(function() {
  if (this.status === 'offline' && this.lastOfflineAt) {
    return Date.now() - this.lastOfflineAt.getTime();
  }
  return 0;
});

// 虚拟字段：设备健康状态
deviceDataSchema.virtual('healthStatus').get(function() {
  const now = Date.now();
  const lastOnline = this.lastOnlineAt ? this.lastOnlineAt.getTime() : 0;
  const offlineTime = now - lastOnline;
  
  if (this.status === 'error') return 'error';
  if (this.status === 'offline' && offlineTime > 24 * 60 * 60 * 1000) return 'critical'; // 24小时
  if (this.status === 'offline' && offlineTime > 60 * 60 * 1000) return 'warning'; // 1小时
  if (this.status === 'online') return 'good';
  
  return 'unknown';
});

// 静态方法：获取用户的所有设备
deviceDataSchema.statics.getByUser = function(userId, type = null) {
  const query = { userId };
  if (type) query.type = type;
  
  return this.find(query).sort({ lastOnlineAt: -1 });
};

// 静态方法：获取在线设备
deviceDataSchema.statics.getOnlineDevices = function(userId = null) {
  const query = { status: 'online' };
  if (userId) query.userId = userId;
  
  return this.find(query).sort({ lastOnlineAt: -1 });
};

// 静态方法：获取离线设备
deviceDataSchema.statics.getOfflineDevices = function(userId = null, hours = 1) {
  const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
  const query = { 
    status: 'offline',
    lastOfflineAt: { $lt: cutoffTime }
  };
  if (userId) query.userId = userId;
  
  return this.find(query).sort({ lastOfflineAt: 1 });
};

// 实例方法：更新设备状态
deviceDataSchema.methods.updateStatus = function(status, timestamp = new Date()) {
  const oldStatus = this.status;
  this.status = status;
  this.lastUpdatedAt = timestamp;
  
  if (status === 'online' && oldStatus !== 'online') {
    this.lastOnlineAt = timestamp;
  } else if (status === 'offline' && oldStatus !== 'offline') {
    this.lastOfflineAt = timestamp;
  }
  
  return this.save();
};

// 实例方法：更新配置
deviceDataSchema.methods.updateConfig = function(configPath, value) {
  this.set(`config.${configPath}`, value);
  this.lastUpdatedAt = new Date();
  return this.save();
};

// 实例方法：增加统计计数
deviceDataSchema.methods.incrementStat = function(statPath, increment = 1) {
  this.set(`stats.${statPath}`, (this.get(`stats.${statPath}`) || 0) + increment);
  this.lastUpdatedAt = new Date();
  return this.save();
};

// 实例方法：重置统计信息
deviceDataSchema.methods.resetStats = function() {
  this.stats = {
    uptime: 0,
    restartCount: 0,
    errorCount: 0,
    dataTransfer: { sent: 0, received: 0 },
    voice: { wakeupCount: 0, commandCount: 0, successRate: 0 },
    sensorReadings: 0
  };
  this.lastUpdatedAt = new Date();
  return this.save();
};

// 中间件：保存前更新时间戳
deviceDataSchema.pre('save', function(next) {
  this.lastUpdatedAt = new Date();
  next();
});

// 中间件：删除前清理相关数据
deviceDataSchema.pre('remove', function(next) {
  // 可以在这里添加删除设备时的清理逻辑
  // 例如删除相关的传感器数据、通知等
  next();
});

module.exports = mongoose.model('DeviceData', deviceDataSchema);
