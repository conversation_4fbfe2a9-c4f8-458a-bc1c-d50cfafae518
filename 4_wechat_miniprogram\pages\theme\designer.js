/**
 * 主题设计器页面
 * <AUTHOR> Team
 * @version 1.0.0
 */

const app = getApp();
const api = require('../../utils/api');

Page({
  data: {
    // 主题数据
    theme: {
      id: null,
      name: '新主题',
      description: '',
      category: 'custom',
      preview: '',
      config: {
        background: {
          type: 'color', // color, gradient, image
          color: '#1E1E1E',
          gradient: {
            type: 'linear',
            colors: ['#1E1E1E', '#2A2A2A'],
            direction: 'to bottom'
          },
          image: ''
        },
        watchface: {
          style: 'digital', // digital, analog, mixed
          position: { x: 240, y: 240 },
          size: 120,
          color: '#FFFFFF',
          font: 'default'
        },
        widgets: []
      }
    },
    
    // 编辑状态
    isEditing: false,
    hasChanges: false,
    
    // 当前选中元素
    selectedElement: null,
    
    // 工具栏状态
    showToolbar: true,
    currentTool: 'select', // select, text, image, widget
    
    // 属性面板
    showProperties: false,
    
    // 预览模式
    previewMode: false,
    
    // 组件库
    widgets: [
      { type: 'clock', name: '时钟', icon: 'clock-o' },
      { type: 'weather', name: '天气', icon: 'weather' },
      { type: 'battery', name: '电量', icon: 'battery' },
      { type: 'date', name: '日期', icon: 'calendar-o' },
      { type: 'sensor', name: '传感器', icon: 'chart' },
      { type: 'pet', name: '宠物', icon: 'smile-o' }
    ],
    
    // 背景选项
    backgroundTypes: [
      { type: 'color', name: '纯色', icon: 'palette' },
      { type: 'gradient', name: '渐变', icon: 'gradient' },
      { type: 'image', name: '图片', icon: 'photo-o' }
    ],
    
    // 预设颜色
    presetColors: [
      '#1E1E1E', '#2A2A2A', '#3A3A3A', '#4A4A4A',
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
      '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'
    ]
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    console.log('主题设计器加载', options);
    
    if (options.themeId) {
      this.loadTheme(options.themeId);
    } else {
      this.initNewTheme();
    }
  },

  /**
   * 页面卸载
   */
  onUnload() {
    if (this.data.hasChanges) {
      this.autoSave();
    }
  },

  /**
   * 初始化新主题
   */
  initNewTheme() {
    const theme = {
      ...this.data.theme,
      id: null,
      name: `主题_${Date.now()}`,
      config: {
        ...this.data.theme.config,
        widgets: [
          {
            id: 'clock_1',
            type: 'clock',
            position: { x: 240, y: 200 },
            size: { width: 200, height: 80 },
            config: {
              style: 'digital',
              color: '#FFFFFF',
              font: 'default'
            }
          }
        ]
      }
    };
    
    this.setData({ 
      theme,
      isEditing: true 
    });
  },

  /**
   * 加载主题
   */
  async loadTheme(themeId) {
    try {
      app.showLoading('加载主题中...');
      
      const theme = await api.getTheme(themeId);
      
      this.setData({ 
        theme,
        isEditing: true 
      });
      
      console.log('主题加载完成:', theme);
    } catch (error) {
      console.error('加载主题失败:', error);
      app.showError('加载失败');
      
      // 返回上一页
      wx.navigateBack();
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 保存主题
   */
  async saveTheme() {
    try {
      app.showLoading('保存主题中...');
      
      const theme = this.data.theme;
      
      if (theme.id) {
        await api.updateTheme(theme.id, theme);
      } else {
        const newTheme = await api.createTheme(theme);
        this.setData({ 
          'theme.id': newTheme.id,
          'theme': newTheme
        });
      }
      
      this.setData({ hasChanges: false });
      app.showSuccess('保存成功');
      
    } catch (error) {
      console.error('保存主题失败:', error);
      app.showError('保存失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 自动保存
   */
  async autoSave() {
    if (!this.data.hasChanges) {
      return;
    }
    
    try {
      const theme = this.data.theme;
      
      if (theme.id) {
        await api.updateTheme(theme.id, theme);
        console.log('自动保存完成');
      }
    } catch (error) {
      console.error('自动保存失败:', error);
    }
  },

  /**
   * 预览主题
   */
  onPreview() {
    this.setData({ previewMode: !this.data.previewMode });
  },

  /**
   * 切换工具
   */
  onToolChange(e) {
    const { tool } = e.currentTarget.dataset;
    this.setData({ currentTool: tool });
  },

  /**
   * 添加组件
   */
  onAddWidget(e) {
    const { widget } = e.currentTarget.dataset;
    
    const newWidget = {
      id: `${widget.type}_${Date.now()}`,
      type: widget.type,
      position: { x: 240, y: 300 },
      size: { width: 120, height: 60 },
      config: this.getDefaultWidgetConfig(widget.type)
    };
    
    const widgets = [...this.data.theme.config.widgets, newWidget];
    
    this.setData({ 
      'theme.config.widgets': widgets,
      selectedElement: newWidget,
      hasChanges: true
    });
  },

  /**
   * 获取默认组件配置
   */
  getDefaultWidgetConfig(type) {
    const configs = {
      clock: {
        style: 'digital',
        color: '#FFFFFF',
        font: 'default'
      },
      weather: {
        showIcon: true,
        showTemp: true,
        color: '#FFFFFF'
      },
      battery: {
        showPercent: true,
        color: '#4ECDC4'
      },
      date: {
        format: 'YYYY-MM-DD',
        color: '#FFFFFF'
      },
      sensor: {
        type: 'temperature',
        color: '#45B7D1'
      },
      pet: {
        emotion: 'happy',
        size: 'medium'
      }
    };
    
    return configs[type] || {};
  },

  /**
   * 选中元素
   */
  onSelectElement(e) {
    const { element } = e.currentTarget.dataset;
    this.setData({ 
      selectedElement: element,
      showProperties: true
    });
  },

  /**
   * 删除元素
   */
  onDeleteElement() {
    const selectedElement = this.data.selectedElement;
    if (!selectedElement) return;
    
    const widgets = this.data.theme.config.widgets.filter(
      widget => widget.id !== selectedElement.id
    );
    
    this.setData({ 
      'theme.config.widgets': widgets,
      selectedElement: null,
      showProperties: false,
      hasChanges: true
    });
  },

  /**
   * 更新背景
   */
  onBackgroundChange(e) {
    const { type, value } = e.currentTarget.dataset;
    
    this.setData({ 
      [`theme.config.background.${type}`]: value,
      hasChanges: true
    });
  },

  /**
   * 发布主题
   */
  async onPublishTheme() {
    if (!this.data.theme.id) {
      app.showError('请先保存主题');
      return;
    }
    
    try {
      app.showLoading('发布主题中...');
      
      await api.publishTheme(this.data.theme.id);
      
      app.showSuccess('发布成功');
      
      // 返回主题列表
      wx.navigateBack();
    } catch (error) {
      console.error('发布主题失败:', error);
      app.showError('发布失败');
    } finally {
      app.hideLoading();
    }
  }
});
