/**
 * 宠物系统集成头文件
 * <AUTHOR> Team
 * @version 1.0.0
 */

#ifndef PET_INTEGRATION_H
#define PET_INTEGRATION_H

#include "esp_err.h"
#include "pet_system.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// 任务事件类型
typedef enum {
    TASK_EVENT_CREATED = 0,
    TASK_EVENT_STARTED,
    TASK_EVENT_COMPLETED,
    TASK_EVENT_FAILED,
    TASK_EVENT_DELETED,
    TASK_EVENT_MAX
} task_event_t;

// 闹钟事件类型
typedef enum {
    ALARM_EVENT_TRIGGERED = 0,
    ALARM_EVENT_SNOOZED,
    ALARM_EVENT_DISMISSED,
    ALARM_EVENT_CREATED,
    ALARM_EVENT_DELETED,
    ALARM_EVENT_MAX
} alarm_event_t;

// 场景类型
typedef enum {
    SCENE_IDLE = 0,
    SCENE_MORNING_WAKE,
    SCENE_SLEEP_AID,
    SCENE_FOCUS_MODE,
    SCENE_MUSIC_RHYTHM,
    SCENE_CONVERSATION,
    SCENE_ALARM,
    SCENE_NOTIFICATION,
    SCENE_MAX
} scene_type_t;

// 宠物集成配置
typedef struct {
    bool enable_voice_integration;      // 启用语音集成
    bool enable_task_integration;       // 启用任务集成
    bool enable_alarm_integration;      // 启用闹钟集成
    bool enable_scene_integration;      // 启用场景集成
    bool enable_ui_integration;         // 启用UI集成
    bool enable_cloud_sync;             // 启用云端同步
    uint16_t emotion_update_interval;   // 情感更新间隔(ms)
    uint8_t interaction_sensitivity;    // 交互敏感度(0-100)
} pet_integration_config_t;

// 宠物集成状态
typedef struct {
    char name[32];                      // 宠物名称
    pet_type_t type;                    // 宠物类型
    pet_emotion_t emotion;              // 当前情感
    uint8_t emotion_intensity;          // 情感强度
    uint8_t health;                     // 健康值
    uint8_t happiness;                  // 快乐值
    uint16_t level;                     // 等级
    uint32_t experience;                // 经验值
    bool enabled;                       // 是否启用
} pet_integration_status_t;

// 函数声明

/**
 * 初始化宠物集成系统
 */
esp_err_t pet_integration_init(const pet_integration_config_t* config);

/**
 * 反初始化宠物集成系统
 */
esp_err_t pet_integration_deinit(void);

/**
 * 手动触发宠物交互
 */
esp_err_t pet_integration_trigger_interaction(pet_interaction_t type, const char* content);

/**
 * 获取宠物当前状态
 */
esp_err_t pet_integration_get_status(pet_integration_status_t* status);

/**
 * 宠物情感变化回调
 */
void pet_emotion_changed_callback(pet_emotion_t emotion, uint8_t intensity);

/**
 * 宠物动画变化回调
 */
void pet_animation_changed_callback(pet_animation_t animation);

/**
 * 宠物交互回调
 */
void pet_interaction_callback(pet_interaction_t type, const char* content);

#ifdef __cplusplus
}
#endif

#endif // PET_INTEGRATION_H
