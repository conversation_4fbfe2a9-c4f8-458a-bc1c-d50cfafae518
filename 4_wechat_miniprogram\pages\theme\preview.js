/**
 * 主题预览页面
 * <AUTHOR> Team
 * @version 1.0.0
 */

const app = getApp();
const api = require('../../utils/api');

Page({
  data: {
    // 主题数据
    theme: null,
    
    // 页面状态
    loading: true,
    
    // 预览状态
    isFullscreen: false,
    showControls: true,
    
    // 模拟数据
    mockData: {
      time: '14:30',
      date: '2025-06-30',
      weather: {
        temperature: 25,
        condition: '晴天',
        icon: 'sunny'
      },
      battery: 85,
      sensors: {
        temperature: 23.5,
        humidity: 65,
        co2: 420
      },
      pet: {
        name: 'TIMO',
        emotion: 'happy',
        level: 5
      }
    },
    
    // 动画状态
    animationData: {},
    
    // 评价相关
    showRating: false,
    userRating: 0,
    ratingComment: ''
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    console.log('主题预览页面加载', options);
    
    if (options.themeId) {
      this.loadTheme(options.themeId);
    } else {
      app.showError('主题ID不能为空');
      wx.navigateBack();
    }
  },

  /**
   * 页面显示
   */
  onShow() {
    // 开始动画
    this.startAnimations();
  },

  /**
   * 页面隐藏
   */
  onHide() {
    // 停止动画
    this.stopAnimations();
  },

  /**
   * 加载主题
   */
  async loadTheme(themeId) {
    try {
      app.showLoading('加载主题中...');
      
      const theme = await api.getTheme(themeId);
      
      this.setData({ 
        theme,
        loading: false
      });
      
      // 更新模拟时间
      this.updateMockTime();
      
      console.log('主题加载完成:', theme);
    } catch (error) {
      console.error('加载主题失败:', error);
      app.showError('加载失败');
      wx.navigateBack();
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 更新模拟时间
   */
  updateMockTime() {
    const now = new Date();
    const time = now.toTimeString().slice(0, 5);
    const date = now.toISOString().slice(0, 10);
    
    this.setData({
      'mockData.time': time,
      'mockData.date': date
    });
  },

  /**
   * 开始动画
   */
  startAnimations() {
    // 时钟动画
    this.clockAnimation = setInterval(() => {
      this.updateMockTime();
    }, 1000);
    
    // 宠物动画
    this.petAnimation = setInterval(() => {
      const emotions = ['happy', 'excited', 'sleepy', 'playful'];
      const randomEmotion = emotions[Math.floor(Math.random() * emotions.length)];
      
      this.setData({
        'mockData.pet.emotion': randomEmotion
      });
    }, 3000);
    
    // 传感器数据动画
    this.sensorAnimation = setInterval(() => {
      const temperature = 20 + Math.random() * 10;
      const humidity = 50 + Math.random() * 30;
      const co2 = 400 + Math.random() * 200;
      
      this.setData({
        'mockData.sensors.temperature': Math.round(temperature * 10) / 10,
        'mockData.sensors.humidity': Math.round(humidity),
        'mockData.sensors.co2': Math.round(co2)
      });
    }, 2000);
  },

  /**
   * 停止动画
   */
  stopAnimations() {
    if (this.clockAnimation) {
      clearInterval(this.clockAnimation);
    }
    if (this.petAnimation) {
      clearInterval(this.petAnimation);
    }
    if (this.sensorAnimation) {
      clearInterval(this.sensorAnimation);
    }
  },

  /**
   * 切换全屏模式
   */
  onToggleFullscreen() {
    this.setData({ 
      isFullscreen: !this.data.isFullscreen,
      showControls: this.data.isFullscreen
    });
  },

  /**
   * 切换控制栏显示
   */
  onToggleControls() {
    if (this.data.isFullscreen) {
      this.setData({ showControls: !this.data.showControls });
    }
  },

  /**
   * 应用主题
   */
  async onApplyTheme() {
    try {
      app.showLoading('应用主题中...');
      
      await api.applyTheme(this.data.theme.id);
      
      // 更新全局当前主题
      app.globalData.currentTheme = this.data.theme;
      
      app.showSuccess('主题应用成功');
      
      // 返回主题列表
      wx.navigateBack();
    } catch (error) {
      console.error('应用主题失败:', error);
      app.showError('应用失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 下载主题
   */
  async onDownloadTheme() {
    try {
      app.showLoading('下载主题中...');
      
      await api.downloadTheme(this.data.theme.id);
      
      app.showSuccess('主题下载成功');
    } catch (error) {
      console.error('下载主题失败:', error);
      app.showError('下载失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 显示评价
   */
  onShowRating() {
    this.setData({ showRating: true });
  },

  /**
   * 隐藏评价
   */
  onHideRating() {
    this.setData({ 
      showRating: false,
      userRating: 0,
      ratingComment: ''
    });
  },

  /**
   * 评分
   */
  onRating(e) {
    const { rating } = e.currentTarget.dataset;
    this.setData({ userRating: rating });
  },

  /**
   * 评论输入
   */
  onCommentInput(e) {
    this.setData({ ratingComment: e.detail.value });
  },

  /**
   * 提交评价
   */
  async onSubmitRating() {
    if (this.data.userRating === 0) {
      app.showError('请选择评分');
      return;
    }
    
    try {
      app.showLoading('提交评价中...');
      
      await api.rateTheme(this.data.theme.id, {
        rating: this.data.userRating,
        comment: this.data.ratingComment
      });
      
      app.showSuccess('评价提交成功');
      this.onHideRating();
    } catch (error) {
      console.error('提交评价失败:', error);
      app.showError('提交失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 分享主题
   */
  onShareAppMessage() {
    return {
      title: `TIMO主题：${this.data.theme.name}`,
      path: `/pages/theme/preview?themeId=${this.data.theme.id}`,
      imageUrl: this.data.theme.preview
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: `TIMO主题：${this.data.theme.name}`,
      imageUrl: this.data.theme.preview
    };
  },

  /**
   * 编辑主题
   */
  onEditTheme() {
    wx.navigateTo({
      url: `/pages/theme/designer?themeId=${this.data.theme.id}`
    });
  },

  /**
   * 举报主题
   */
  onReportTheme() {
    wx.showActionSheet({
      itemList: ['内容不当', '版权问题', '恶意软件', '其他'],
      success: (res) => {
        const reasons = ['inappropriate', 'copyright', 'malware', 'other'];
        const reason = reasons[res.tapIndex];
        
        this.submitReport(reason);
      }
    });
  },

  /**
   * 提交举报
   */
  async submitReport(reason) {
    try {
      await api.reportTheme(this.data.theme.id, { reason });
      app.showSuccess('举报已提交');
    } catch (error) {
      console.error('提交举报失败:', error);
      app.showError('举报失败');
    }
  }
});
