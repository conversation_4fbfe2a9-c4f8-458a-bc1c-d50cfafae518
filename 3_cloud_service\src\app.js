/**
 * TIMO智能闹钟云端服务主应用
 * <AUTHOR> Team
 * @version 1.0.0
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const mongoose = require('mongoose');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

const logger = require('./utils/logger');
const errorHandler = require('./middleware/errorHandler');
const authMiddleware = require('./middleware/auth');
const mqttService = require('./services/mqttService');

// 导入路由
const deviceRoutes = require('./routes/device');
const userRoutes = require('./routes/user');
const dataRoutes = require('./routes/data');
const voiceRoutes = require('./routes/voice');
const taskRoutes = require('./routes/task');
const themeRoutes = require('./routes/theme');
const notificationRoutes = require('./routes/notification');
const mqttRoutes = require('./routes/mqtt');
const mcpRoutes = require('./routes/mcp');
const petRoutes = require('./routes/pet');

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "*",
    methods: ["GET", "POST"]
  }
});

// 基础中间件
app.use(helmet());
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 请求日志
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path} - ${req.ip}`);
  next();
});

// 速率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    error: 'Too many requests from this IP, please try again later.'
  }
});
app.use('/api/', limiter);

// 数据库连接
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/timo', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  logger.info('Connected to MongoDB');
})
.catch((error) => {
  logger.error('MongoDB connection error:', error);
  process.exit(1);
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

// API路由
app.use('/api/device', deviceRoutes);
app.use('/api/user', userRoutes);
app.use('/api/data', dataRoutes);
app.use('/api/voice', voiceRoutes);
app.use('/api/task', taskRoutes);
app.use('/api/theme', themeRoutes);
app.use('/api/notification', notificationRoutes);
app.use('/api/mqtt', mqttRoutes);
app.use('/api/mcp', mcpRoutes);
app.use('/api/pet', petRoutes);

// WebSocket连接处理
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`);
  
  // 设备连接
  socket.on('device:connect', (deviceData) => {
    logger.info(`Device connected: ${deviceData.deviceId}`);
    socket.join(`device:${deviceData.deviceId}`);
    
    // 通知设备状态更新
    socket.to(`device:${deviceData.deviceId}`).emit('device:status', {
      deviceId: deviceData.deviceId,
      status: 'online',
      timestamp: new Date().toISOString()
    });
  });
  
  // 设备断开连接
  socket.on('device:disconnect', (deviceData) => {
    logger.info(`Device disconnected: ${deviceData.deviceId}`);
    socket.leave(`device:${deviceData.deviceId}`);
    
    // 通知设备状态更新
    socket.to(`device:${deviceData.deviceId}`).emit('device:status', {
      deviceId: deviceData.deviceId,
      status: 'offline',
      timestamp: new Date().toISOString()
    });
  });
  
  // 实时数据推送
  socket.on('data:sensor', (sensorData) => {
    // 广播传感器数据到所有订阅的客户端
    socket.broadcast.emit('data:sensor:update', sensorData);
  });
  
  // 语音对话
  socket.on('voice:chat', async (voiceData) => {
    try {
      // 处理语音对话逻辑
      const response = await processVoiceChat(voiceData);
      socket.emit('voice:response', response);
    } catch (error) {
      logger.error('Voice chat error:', error);
      socket.emit('voice:error', { message: 'Voice processing failed' });
    }
  });
  
  // 客户端断开连接
  socket.on('disconnect', () => {
    logger.info(`Client disconnected: ${socket.id}`);
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`
  });
});

// 错误处理中间件
app.use(errorHandler);

// 语音对话处理函数
async function processVoiceChat(voiceData) {
  // TODO: 实现语音对话逻辑
  // 1. ASR - 语音转文本
  // 2. NLU - 自然语言理解
  // 3. 对话管理
  // 4. TTS - 文本转语音
  
  return {
    text: "这是一个示例回复",
    audio: null,
    timestamp: new Date().toISOString()
  };
}

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    mongoose.connection.close();
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    mongoose.connection.close();
    process.exit(0);
  });
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, async () => {
  logger.info(`TIMO Cloud Service running on port ${PORT}`);
  logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);

  // 初始化MQTT服务
  try {
    await mqttService.connect();
    logger.info('MQTT服务初始化成功');
  } catch (error) {
    logger.error('MQTT服务初始化失败:', error);
  }
});

module.exports = app;
