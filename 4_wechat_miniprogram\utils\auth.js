/**
 * 认证管理工具
 */

const api = require('./api');

class Auth {
  constructor() {
    this.token = null;
    this.userInfo = null;
  }

  /**
   * 微信登录
   */
  async login(userInfo = null) {
    try {
      // 获取微信登录code
      const loginResult = await this.getWxLoginCode();
      const code = loginResult.code;

      // 如果没有传入用户信息，尝试获取
      if (!userInfo) {
        userInfo = await this.getWxUserInfo();
      }

      // 调用后端登录接口
      const result = await api.wxLogin(code, userInfo);

      // 保存token和用户信息
      this.setToken(result.token);
      this.setUserInfo(result.user);

      return result;
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  }

  /**
   * 获取微信登录code
   */
  getWxLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  }

  /**
   * 获取微信用户信息
   */
  getWxUserInfo() {
    return new Promise((resolve, reject) => {
      // 检查是否已授权
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.userInfo']) {
            // 已授权，直接获取用户信息
            wx.getUserInfo({
              success: (userRes) => {
                resolve(userRes.userInfo);
              },
              fail: reject
            });
          } else {
            // 未授权，需要用户主动授权
            resolve(null);
          }
        },
        fail: reject
      });
    });
  }

  /**
   * 获取用户信息（通过按钮授权）
   */
  getUserInfoByButton(e) {
    return new Promise((resolve, reject) => {
      if (e.detail.userInfo) {
        resolve(e.detail.userInfo);
      } else {
        reject(new Error('用户拒绝授权'));
      }
    });
  }

  /**
   * 设置token
   */
  setToken(token) {
    this.token = token;
    wx.setStorageSync('token', token);
  }

  /**
   * 获取token
   */
  getToken() {
    if (!this.token) {
      this.token = wx.getStorageSync('token');
    }
    return this.token;
  }

  /**
   * 清除token
   */
  clearToken() {
    this.token = null;
    wx.removeStorageSync('token');
  }

  /**
   * 设置用户信息
   */
  setUserInfo(userInfo) {
    this.userInfo = userInfo;
    wx.setStorageSync('userInfo', userInfo);
  }

  /**
   * 获取用户信息
   */
  getUserInfo() {
    if (!this.userInfo) {
      this.userInfo = wx.getStorageSync('userInfo');
    }
    return this.userInfo;
  }

  /**
   * 清除用户信息
   */
  clearUserInfo() {
    this.userInfo = null;
    wx.removeStorageSync('userInfo');
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn() {
    return !!this.getToken();
  }

  /**
   * 登出
   */
  logout() {
    this.clearToken();
    this.clearUserInfo();
    
    // 跳转到登录页
    wx.reLaunch({
      url: '/pages/login/login'
    });
  }

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    if (!this.isLoggedIn()) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: () => {
          wx.navigateTo({
            url: '/pages/login/login'
          });
        }
      });
      return false;
    }
    return true;
  }

  /**
   * 获取手机号
   */
  getPhoneNumber(e) {
    return new Promise((resolve, reject) => {
      if (e.detail.errMsg === 'getPhoneNumber:ok') {
        // 需要发送到后端解密
        resolve(e.detail);
      } else {
        reject(new Error('获取手机号失败'));
      }
    });
  }

  /**
   * 请求用户授权
   */
  requestAuth(scope) {
    return new Promise((resolve, reject) => {
      wx.getSetting({
        success: (res) => {
          if (res.authSetting[scope]) {
            // 已授权
            resolve(true);
          } else if (res.authSetting[scope] === false) {
            // 用户拒绝过，需要引导到设置页面
            wx.showModal({
              title: '授权提示',
              content: '需要您的授权才能正常使用功能，请在设置中开启',
              confirmText: '去设置',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  wx.openSetting({
                    success: (settingRes) => {
                      resolve(settingRes.authSetting[scope]);
                    },
                    fail: reject
                  });
                } else {
                  resolve(false);
                }
              }
            });
          } else {
            // 首次请求授权
            wx.authorize({
              scope,
              success: () => resolve(true),
              fail: () => resolve(false)
            });
          }
        },
        fail: reject
      });
    });
  }

  /**
   * 检查位置授权
   */
  async checkLocationAuth() {
    return this.requestAuth('scope.userLocation');
  }

  /**
   * 检查蓝牙授权
   */
  async checkBluetoothAuth() {
    return this.requestAuth('scope.bluetooth');
  }

  /**
   * 检查摄像头授权
   */
  async checkCameraAuth() {
    return this.requestAuth('scope.camera');
  }
}

// 创建认证实例
const auth = new Auth();

module.exports = auth;
