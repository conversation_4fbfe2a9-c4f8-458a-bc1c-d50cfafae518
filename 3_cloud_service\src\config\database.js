/**
 * 数据库配置
 * <AUTHOR> Team
 * @version 1.0.0
 */

const mongoose = require('mongoose');
const logger = require('../utils/logger');

class DatabaseConfig {
  constructor() {
    this.connection = null;
    this.isConnected = false;
  }

  /**
   * 连接数据库
   */
  async connect() {
    try {
      const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/timo';
      
      const options = {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        bufferMaxEntries: 0,
        bufferCommands: false,
      };

      this.connection = await mongoose.connect(mongoUri, options);
      this.isConnected = true;

      logger.info('MongoDB connected successfully');
      
      // 监听连接事件
      this.setupEventListeners();
      
      return this.connection;
    } catch (error) {
      logger.error('MongoDB connection error:', error);
      throw error;
    }
  }

  /**
   * 断开数据库连接
   */
  async disconnect() {
    try {
      if (this.connection) {
        await mongoose.disconnect();
        this.isConnected = false;
        logger.info('MongoDB disconnected');
      }
    } catch (error) {
      logger.error('MongoDB disconnect error:', error);
      throw error;
    }
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    mongoose.connection.on('connected', () => {
      logger.info('Mongoose connected to MongoDB');
      this.isConnected = true;
    });

    mongoose.connection.on('error', (error) => {
      logger.error('Mongoose connection error:', error);
      this.isConnected = false;
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('Mongoose disconnected from MongoDB');
      this.isConnected = false;
    });

    // 应用终止时关闭数据库连接
    process.on('SIGINT', async () => {
      await this.disconnect();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      await this.disconnect();
      process.exit(0);
    });
  }

  /**
   * 检查连接状态
   */
  isConnectedToDatabase() {
    return this.isConnected && mongoose.connection.readyState === 1;
  }

  /**
   * 获取数据库统计信息
   */
  async getStats() {
    try {
      if (!this.isConnectedToDatabase()) {
        throw new Error('Database not connected');
      }

      const admin = mongoose.connection.db.admin();
      const stats = await admin.serverStatus();
      
      return {
        version: stats.version,
        uptime: stats.uptime,
        connections: stats.connections,
        memory: stats.mem,
        network: stats.network,
        opcounters: stats.opcounters
      };
    } catch (error) {
      logger.error('Get database stats error:', error);
      throw error;
    }
  }

  /**
   * 创建索引
   */
  async createIndexes() {
    try {
      logger.info('Creating database indexes...');

      // 用户集合索引
      await mongoose.connection.db.collection('users').createIndexes([
        { key: { email: 1 }, unique: true },
        { key: { username: 1 }, unique: true },
        { key: { createdAt: -1 } }
      ]);

      // 设备数据集合索引
      await mongoose.connection.db.collection('device_data').createIndexes([
        { key: { deviceId: 1 }, unique: true },
        { key: { userId: 1, type: 1 } },
        { key: { status: 1, lastOnlineAt: -1 } }
      ]);

      // 传感器数据集合索引
      await mongoose.connection.db.collection('sensor_data').createIndexes([
        { key: { deviceId: 1, sensorType: 1, timestamp: -1 } },
        { key: { userId: 1, timestamp: -1 } },
        { key: { timestamp: -1 } }
      ]);

      // 任务集合索引
      await mongoose.connection.db.collection('tasks').createIndexes([
        { key: { userId: 1, status: 1 } },
        { key: { dueDate: 1 } },
        { key: { createdAt: -1 } }
      ]);

      // 通知集合索引
      await mongoose.connection.db.collection('notifications').createIndexes([
        { key: { userId: 1, status: 1, createdAt: -1 } },
        { key: { deviceId: 1, type: 1 } },
        { key: { expiresAt: 1 }, expireAfterSeconds: 0 }
      ]);

      // 主题集合索引
      await mongoose.connection.db.collection('themes').createIndexes([
        { key: { category: 1, status: 1, featured: -1 } },
        { key: { 'stats.rating': -1, 'stats.downloads': -1 } },
        { key: { tags: 1 } }
      ]);

      logger.info('Database indexes created successfully');
    } catch (error) {
      logger.error('Create indexes error:', error);
      throw error;
    }
  }

  /**
   * 数据库健康检查
   */
  async healthCheck() {
    try {
      if (!this.isConnectedToDatabase()) {
        return {
          status: 'error',
          message: 'Database not connected'
        };
      }

      // 执行简单查询测试连接
      await mongoose.connection.db.admin().ping();

      return {
        status: 'ok',
        message: 'Database connection healthy',
        readyState: mongoose.connection.readyState,
        host: mongoose.connection.host,
        port: mongoose.connection.port,
        name: mongoose.connection.name
      };
    } catch (error) {
      return {
        status: 'error',
        message: error.message
      };
    }
  }

  /**
   * 备份数据库
   */
  async backup(collections = []) {
    try {
      logger.info('Starting database backup...');

      const backupData = {};
      const collectionsToBackup = collections.length > 0 ? collections : [
        'users', 'device_data', 'sensor_data', 'tasks', 'notifications', 'themes'
      ];

      for (const collectionName of collectionsToBackup) {
        const collection = mongoose.connection.db.collection(collectionName);
        const data = await collection.find({}).toArray();
        backupData[collectionName] = data;
        logger.info(`Backed up ${data.length} documents from ${collectionName}`);
      }

      const backupInfo = {
        timestamp: new Date(),
        collections: Object.keys(backupData),
        totalDocuments: Object.values(backupData).reduce((sum, docs) => sum + docs.length, 0),
        data: backupData
      };

      logger.info('Database backup completed');
      return backupInfo;
    } catch (error) {
      logger.error('Database backup error:', error);
      throw error;
    }
  }

  /**
   * 清理过期数据
   */
  async cleanupExpiredData() {
    try {
      logger.info('Starting cleanup of expired data...');

      const results = {};

      // 清理过期通知
      const expiredNotifications = await mongoose.connection.db.collection('notifications')
        .deleteMany({ expiresAt: { $lt: new Date() } });
      results.notifications = expiredNotifications.deletedCount;

      // 清理旧的传感器数据（保留30天）
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const oldSensorData = await mongoose.connection.db.collection('sensor_data')
        .deleteMany({ timestamp: { $lt: thirtyDaysAgo } });
      results.sensorData = oldSensorData.deletedCount;

      // 清理已完成的旧任务（保留90天）
      const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
      const oldTasks = await mongoose.connection.db.collection('tasks')
        .deleteMany({ 
          status: 'completed',
          completedAt: { $lt: ninetyDaysAgo }
        });
      results.tasks = oldTasks.deletedCount;

      logger.info('Cleanup completed:', results);
      return results;
    } catch (error) {
      logger.error('Cleanup expired data error:', error);
      throw error;
    }
  }
}

module.exports = new DatabaseConfig();
