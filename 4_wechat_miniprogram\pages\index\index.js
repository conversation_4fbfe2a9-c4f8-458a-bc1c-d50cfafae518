/**
 * 首页
 */

const app = getApp();
const api = require('../../utils/api');
const auth = require('../../utils/auth');
const websocket = require('../../utils/websocket');

Page({
  data: {
    // 用户信息
    userInfo: null,
    
    // 设备信息
    devices: [],
    currentDevice: null,
    deviceStatus: 'offline',
    
    // 传感器数据
    sensorData: {
      temperature: '--',
      humidity: '--',
      co2: '--',
      light: '--',
      comfort: '--'
    },
    
    // 今日任务
    todayTasks: [],
    taskStats: {
      total: 0,
      completed: 0,
      pending: 0
    },
    
    // 宠物信息
    pets: [],
    activePet: null,
    
    // 页面状态
    loading: true,
    refreshing: false,
    
    // 快捷功能
    quickActions: [
      {
        id: 'scene',
        name: '场景控制',
        icon: '/images/icons/scene.png',
        url: '/pages/scene/list'
      },
      {
        id: 'alarm',
        name: '闹钟设置',
        icon: '/images/icons/alarm.png',
        url: '/pages/alarm/alarm'
      },
      {
        id: 'pomodoro',
        name: '番茄时钟',
        icon: '/images/icons/pomodoro.png',
        url: '/pages/task/pomodoro'
      },
      {
        id: 'theme',
        name: '主题商店',
        icon: '/images/icons/theme.png',
        url: '/pages/theme/store'
      }
    ]
  },

  /**
   * 页面加载
   */
  onLoad() {
    console.log('首页加载');
    
    // 检查登录状态
    if (!auth.isLoggedIn()) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return;
    }
    
    // 初始化页面数据
    this.initPageData();
    
    // 设置WebSocket监听
    this.setupWebSocketListeners();
  },

  /**
   * 页面显示
   */
  onShow() {
    console.log('首页显示');
    
    // 刷新页面数据
    if (!this.data.loading) {
      this.refreshPageData();
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshPageData();
  },

  /**
   * 初始化页面数据
   */
  async initPageData() {
    try {
      app.showLoading('加载中...');
      
      // 并行加载数据
      await Promise.all([
        this.loadUserInfo(),
        this.loadDevices(),
        this.loadTodayTasks(),
        this.loadPets()
      ]);
      
      // 如果有设备，加载传感器数据
      if (this.data.currentDevice) {
        await this.loadSensorData();
      }
      
    } catch (error) {
      console.error('初始化页面数据失败:', error);
      app.showError('加载失败');
    } finally {
      this.setData({ loading: false });
      app.hideLoading();
    }
  },

  /**
   * 刷新页面数据
   */
  async refreshPageData() {
    this.setData({ refreshing: true });
    
    try {
      await Promise.all([
        this.loadDevices(),
        this.loadTodayTasks(),
        this.loadPets()
      ]);
      
      if (this.data.currentDevice) {
        await this.loadSensorData();
      }
      
    } catch (error) {
      console.error('刷新页面数据失败:', error);
      app.showError('刷新失败');
    } finally {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      const userInfo = await api.getUserInfo();
      this.setData({ userInfo });
      app.globalData.userInfo = userInfo;
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  },

  /**
   * 加载设备列表
   */
  async loadDevices() {
    try {
      const devices = await api.getDevices();
      this.setData({ devices });
      app.globalData.devices = devices;
      
      // 设置当前设备
      const currentDevice = app.globalData.currentDevice || devices[0];
      if (currentDevice) {
        this.setData({ 
          currentDevice,
          deviceStatus: currentDevice.isOnline ? 'online' : 'offline'
        });
        app.globalData.currentDevice = currentDevice;
      }
    } catch (error) {
      console.error('加载设备列表失败:', error);
    }
  },

  /**
   * 加载今日任务
   */
  async loadTodayTasks() {
    try {
      const [todayTasks, taskStats] = await Promise.all([
        api.getTodayTasks(),
        api.getTaskStats()
      ]);
      
      this.setData({ 
        todayTasks: todayTasks.tasks || [],
        taskStats: taskStats.stats || {}
      });
    } catch (error) {
      console.error('加载今日任务失败:', error);
    }
  },

  /**
   * 加载宠物信息
   */
  async loadPets() {
    try {
      const pets = await api.getPets();
      const activePet = pets.pets?.find(pet => pet.isActive) || pets.pets?.[0];
      
      this.setData({ 
        pets: pets.pets || [],
        activePet
      });
    } catch (error) {
      console.error('加载宠物信息失败:', error);
    }
  },

  /**
   * 加载传感器数据
   */
  async loadSensorData() {
    try {
      const data = await api.getLatestSensorData(this.data.currentDevice.deviceId);
      
      this.setData({
        sensorData: {
          temperature: data.temperature?.toFixed(1) || '--',
          humidity: data.humidity?.toFixed(1) || '--',
          co2: data.co2 || '--',
          light: data.light?.toFixed(0) || '--',
          comfort: data.comfort || '--'
        }
      });
    } catch (error) {
      console.error('加载传感器数据失败:', error);
    }
  },

  /**
   * 设置WebSocket监听
   */
  setupWebSocketListeners() {
    // 监听设备状态变化
    websocket.on('deviceStatus', (data) => {
      if (data.deviceId === this.data.currentDevice?.deviceId) {
        this.setData({
          deviceStatus: data.status
        });
      }
    });
    
    // 监听传感器数据更新
    websocket.on('sensorData', (data) => {
      if (data.deviceId === this.data.currentDevice?.deviceId) {
        this.setData({
          sensorData: {
            temperature: data.temperature?.toFixed(1) || '--',
            humidity: data.humidity?.toFixed(1) || '--',
            co2: data.co2 || '--',
            light: data.light?.toFixed(0) || '--',
            comfort: data.comfort || '--'
          }
        });
      }
    });
    
    // 监听宠物状态更新
    websocket.on('petStatus', (data) => {
      if (this.data.activePet && data.petId === this.data.activePet.id) {
        this.setData({
          'activePet.attributes': data.attributes,
          'activePet.emotion': data.emotion
        });
      }
    });
    
    // 监听任务提醒
    websocket.on('taskReminder', (data) => {
      wx.showModal({
        title: '任务提醒',
        content: data.message,
        showCancel: false
      });
    });
  },

  /**
   * 切换设备
   */
  onDeviceChange(e) {
    const deviceIndex = e.detail.value;
    const device = this.data.devices[deviceIndex];
    
    this.setData({ 
      currentDevice: device,
      deviceStatus: device.isOnline ? 'online' : 'offline'
    });
    
    app.setCurrentDevice(device);
    
    // 重新加载传感器数据
    this.loadSensorData();
  },

  /**
   * 快捷操作点击
   */
  onQuickActionTap(e) {
    const { url } = e.currentTarget.dataset;
    wx.navigateTo({ url });
  },

  /**
   * 查看更多设备
   */
  onMoreDevicesTap() {
    wx.switchTab({
      url: '/pages/device/device'
    });
  },

  /**
   * 查看更多任务
   */
  onMoreTasksTap() {
    wx.navigateTo({
      url: '/pages/task/list'
    });
  },

  /**
   * 任务项点击
   */
  onTaskTap(e) {
    const { taskId } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/task/detail?id=${taskId}`
    });
  },

  /**
   * 完成任务
   */
  async onCompleteTask(e) {
    const { taskId, index } = e.currentTarget.dataset;
    
    try {
      await api.completeTask(taskId);
      
      // 更新本地数据
      const todayTasks = [...this.data.todayTasks];
      todayTasks[index].status = 'completed';
      
      this.setData({ todayTasks });
      
      app.showSuccess('任务已完成');
    } catch (error) {
      console.error('完成任务失败:', error);
      app.showError('操作失败');
    }
  },

  /**
   * 宠物交互
   */
  async onPetInteract(e) {
    const { action } = e.currentTarget.dataset;
    
    if (!this.data.activePet) {
      app.showError('暂无活跃宠物');
      return;
    }
    
    try {
      await api.petInteract(this.data.activePet.id, action);
      app.showSuccess('交互成功');
    } catch (error) {
      console.error('宠物交互失败:', error);
      app.showError('交互失败');
    }
  },

  /**
   * 查看宠物详情
   */
  onPetDetailTap() {
    if (this.data.activePet) {
      wx.navigateTo({
        url: `/pages/pet/detail?id=${this.data.activePet.id}`
      });
    }
  }
});
