/**
 * 宠物系统集成实现
 * <AUTHOR> Team
 * @version 1.0.0
 */

#include "pet_integration.h"
#include "pet_system.h"
#include "voice_manager.h"
#include "task_manager.h"
#include "alarm_manager.h"
#include "scene_manager.h"
#include "esp_log.h"
#include <string.h>

static const char* TAG = "PET_INTEGRATION";

// 全局变量
static bool g_pet_integration_initialized = false;
static pet_integration_config_t g_integration_config = {0};

// 私有函数声明
static void pet_voice_interaction_handler(const char* user_input, const char* ai_response);
static void pet_task_event_handler(task_event_t event, uint32_t task_id);
static void pet_alarm_event_handler(alarm_event_t event, uint32_t alarm_id);
static void pet_scene_change_handler(scene_type_t old_scene, scene_type_t new_scene);
static pet_emotion_t analyze_user_emotion_from_voice(const char* text, float tone_confidence);
static void update_pet_based_on_interaction(pet_interaction_t type, const char* content, pet_emotion_t user_emotion);

/**
 * 初始化宠物集成系统
 */
esp_err_t pet_integration_init(const pet_integration_config_t* config) {
    if (g_pet_integration_initialized) {
        ESP_LOGW(TAG, "Pet integration already initialized");
        return ESP_OK;
    }

    if (!config) {
        ESP_LOGE(TAG, "Invalid config parameter");
        return ESP_ERR_INVALID_ARG;
    }

    // 复制配置
    memcpy(&g_integration_config, config, sizeof(pet_integration_config_t));

    // 初始化宠物系统
    pet_system_config_t pet_config = {
        .emotion_callback = pet_emotion_changed_callback,
        .animation_callback = pet_animation_changed_callback,
        .interaction_callback = pet_interaction_callback,
        .update_interval_ms = 1000,
        .animation_fps = 30,
        .auto_emotion_update = true
    };

    esp_err_t ret = pet_system_init(&pet_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize pet system");
        return ret;
    }

    // 注册各种事件回调
    if (g_integration_config.enable_voice_integration) {
        voice_manager_register_interaction_callback(pet_voice_interaction_handler);
    }

    if (g_integration_config.enable_task_integration) {
        task_manager_register_event_callback(pet_task_event_handler);
    }

    if (g_integration_config.enable_alarm_integration) {
        alarm_manager_register_event_callback(pet_alarm_event_handler);
    }

    if (g_integration_config.enable_scene_integration) {
        scene_manager_register_change_callback(pet_scene_change_handler);
    }

    g_pet_integration_initialized = true;
    ESP_LOGI(TAG, "Pet integration system initialized successfully");

    return ESP_OK;
}

/**
 * 反初始化宠物集成系统
 */
esp_err_t pet_integration_deinit(void) {
    if (!g_pet_integration_initialized) {
        return ESP_OK;
    }

    // 反初始化宠物系统
    pet_system_deinit();

    g_pet_integration_initialized = false;
    ESP_LOGI(TAG, "Pet integration system deinitialized");

    return ESP_OK;
}

/**
 * 处理语音交互事件
 */
static void pet_voice_interaction_handler(const char* user_input, const char* ai_response) {
    if (!user_input) {
        return;
    }

    ESP_LOGI(TAG, "Processing voice interaction: user='%s', ai='%s'", user_input, ai_response ? ai_response : "");

    // 分析用户情感
    pet_emotion_t user_emotion = analyze_user_emotion_from_voice(user_input, 0.8f);

    // 更新宠物状态
    update_pet_based_on_interaction(PET_INTERACT_VOICE, user_input, user_emotion);

    // 根据AI回复调整宠物反应
    if (ai_response) {
        if (strstr(ai_response, "开心") || strstr(ai_response, "高兴")) {
            pet_express_emotion(PET_EMOTION_HAPPY, 3000);
        } else if (strstr(ai_response, "帮助") || strstr(ai_response, "协助")) {
            pet_enter_task_mode(true);
            pet_express_emotion(PET_EMOTION_CURIOUS, 2000);
        }
    }
}

/**
 * 处理任务事件
 */
static void pet_task_event_handler(task_event_t event, uint32_t task_id) {
    ESP_LOGI(TAG, "Processing task event: %d, task_id: %d", event, task_id);

    switch (event) {
        case TASK_EVENT_CREATED:
            pet_enter_task_mode(true);
            pet_express_emotion(PET_EMOTION_CURIOUS, 2000);
            pet_speak("有新任务了，让我来帮助你完成吧！");
            break;

        case TASK_EVENT_STARTED:
            pet_set_animation(PET_ANIM_GUIDE);
            pet_express_emotion(PET_EMOTION_EXCITED, 3000);
            pet_speak("开始任务，加油！");
            break;

        case TASK_EVENT_COMPLETED:
            pet_enter_task_mode(false);
            pet_express_emotion(PET_EMOTION_HAPPY, 5000);
            pet_speak("任务完成了！你真棒！");
            break;

        case TASK_EVENT_FAILED:
            pet_express_emotion(PET_EMOTION_SAD, 3000);
            pet_speak("没关系，我们下次再努力！");
            break;

        default:
            break;
    }
}

/**
 * 处理闹钟事件
 */
static void pet_alarm_event_handler(alarm_event_t event, uint32_t alarm_id) {
    ESP_LOGI(TAG, "Processing alarm event: %d, alarm_id: %d", event, alarm_id);

    switch (event) {
        case ALARM_EVENT_TRIGGERED:
            pet_express_emotion(PET_EMOTION_EXCITED, 10000);
            pet_set_animation(PET_ANIM_JUMP);
            pet_speak("主人，该起床了！新的一天开始啦！");
            break;

        case ALARM_EVENT_SNOOZED:
            pet_express_emotion(PET_EMOTION_CALM, 5000);
            pet_speak("好吧，再休息一会儿~");
            break;

        case ALARM_EVENT_DISMISSED:
            pet_express_emotion(PET_EMOTION_HAPPY, 3000);
            pet_speak("早安！今天也要加油哦！");
            break;

        default:
            break;
    }
}

/**
 * 处理场景切换事件
 */
static void pet_scene_change_handler(scene_type_t old_scene, scene_type_t new_scene) {
    ESP_LOGI(TAG, "Processing scene change: %d -> %d", old_scene, new_scene);

    switch (new_scene) {
        case SCENE_MORNING_WAKE:
            pet_express_emotion(PET_EMOTION_EXCITED, 5000);
            pet_speak("早上好！美好的一天开始了！");
            break;

        case SCENE_SLEEP_AID:
            pet_enter_sleep_mode(true);
            pet_express_emotion(PET_EMOTION_SLEEPY, 10000);
            pet_speak("晚安，做个好梦~");
            break;

        case SCENE_FOCUS_MODE:
            pet_enter_task_mode(true);
            pet_express_emotion(PET_EMOTION_CALM, 0);
            pet_speak("专注模式开启，我会安静地陪着你");
            break;

        case SCENE_MUSIC_RHYTHM:
            pet_express_emotion(PET_EMOTION_PLAYFUL, 0);
            pet_set_animation(PET_ANIM_PLAY);
            break;

        case SCENE_CONVERSATION:
            pet_express_emotion(PET_EMOTION_CURIOUS, 0);
            pet_set_animation(PET_ANIM_IDLE);
            break;

        default:
            pet_express_emotion(PET_EMOTION_CALM, 2000);
            break;
    }
}

/**
 * 从语音分析用户情感
 */
static pet_emotion_t analyze_user_emotion_from_voice(const char* text, float tone_confidence) {
    if (!text) {
        return PET_EMOTION_CALM;
    }

    // 简单的关键词情感分析
    if (strstr(text, "开心") || strstr(text, "高兴") || strstr(text, "棒") || strstr(text, "好")) {
        return PET_EMOTION_HAPPY;
    } else if (strstr(text, "难过") || strstr(text, "伤心") || strstr(text, "不开心")) {
        return PET_EMOTION_SAD;
    } else if (strstr(text, "生气") || strstr(text, "愤怒") || strstr(text, "烦")) {
        return PET_EMOTION_ANGRY;
    } else if (strstr(text, "害怕") || strstr(text, "紧张") || strstr(text, "担心")) {
        return PET_EMOTION_SCARED;
    } else if (strstr(text, "累") || strstr(text, "困") || strstr(text, "睡")) {
        return PET_EMOTION_SLEEPY;
    } else if (strstr(text, "玩") || strstr(text, "游戏") || strstr(text, "有趣")) {
        return PET_EMOTION_PLAYFUL;
    } else if (strstr(text, "什么") || strstr(text, "为什么") || strstr(text, "怎么")) {
        return PET_EMOTION_CURIOUS;
    }

    return PET_EMOTION_CALM;
}

/**
 * 根据交互更新宠物状态
 */
static void update_pet_based_on_interaction(pet_interaction_t type, const char* content, pet_emotion_t user_emotion) {
    // 创建交互事件
    pet_event_t event = {
        .type = type,
        .user_emotion = user_emotion,
        .duration = 0,
        .timestamp = esp_timer_get_time() / 1000
    };

    if (content) {
        strncpy(event.content, content, sizeof(event.content) - 1);
        event.content[sizeof(event.content) - 1] = '\0';
    }

    // 处理交互
    pet_process_interaction(&event);

    // 根据用户情感调整宠物反应
    switch (user_emotion) {
        case PET_EMOTION_HAPPY:
            pet_express_emotion(PET_EMOTION_HAPPY, 3000);
            break;
        case PET_EMOTION_SAD:
            pet_express_emotion(PET_EMOTION_SAD, 2000);
            pet_speak("别难过，我会陪着你的");
            break;
        case PET_EMOTION_ANGRY:
            pet_express_emotion(PET_EMOTION_SCARED, 2000);
            pet_speak("主人别生气，深呼吸~");
            break;
        case PET_EMOTION_PLAYFUL:
            pet_express_emotion(PET_EMOTION_PLAYFUL, 5000);
            pet_set_animation(PET_ANIM_PLAY);
            break;
        default:
            pet_express_emotion(PET_EMOTION_CURIOUS, 1000);
            break;
    }
}

/**
 * 宠物情感变化回调
 */
void pet_emotion_changed_callback(pet_emotion_t emotion, uint8_t intensity) {
    ESP_LOGI(TAG, "Pet emotion changed: %d, intensity: %d", emotion, intensity);

    // 根据情感调整界面显示
    if (g_integration_config.enable_ui_integration) {
        // TODO: 通知UI管理器更新宠物显示
    }

    // 根据情感调整灯光效果
    if (g_integration_config.enable_scene_integration) {
        // TODO: 通知场景管理器调整灯光
    }
}

/**
 * 宠物动画变化回调
 */
void pet_animation_changed_callback(pet_animation_t animation) {
    ESP_LOGI(TAG, "Pet animation changed: %d", animation);

    // TODO: 通知UI管理器更新动画
}

/**
 * 宠物交互回调
 */
void pet_interaction_callback(pet_interaction_t type, const char* content) {
    ESP_LOGI(TAG, "Pet interaction: type=%d, content=%s", type, content ? content : "");

    // TODO: 记录交互数据，发送到云端
}

/**
 * 手动触发宠物交互
 */
esp_err_t pet_integration_trigger_interaction(pet_interaction_t type, const char* content) {
    if (!g_pet_integration_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    pet_event_t event = {
        .type = type,
        .user_emotion = PET_EMOTION_CALM,
        .duration = 0,
        .timestamp = esp_timer_get_time() / 1000
    };

    if (content) {
        strncpy(event.content, content, sizeof(event.content) - 1);
        event.content[sizeof(event.content) - 1] = '\0';
    }

    return pet_process_interaction(&event);
}

/**
 * 获取宠物当前状态
 */
esp_err_t pet_integration_get_status(pet_integration_status_t* status) {
    if (!status || !g_pet_integration_initialized) {
        return ESP_ERR_INVALID_ARG;
    }

    pet_config_t pet_config;
    esp_err_t ret = pet_get_config(&pet_config);
    if (ret != ESP_OK) {
        return ret;
    }

    // 填充状态信息
    strncpy(status->name, pet_config.name, sizeof(status->name) - 1);
    status->name[sizeof(status->name) - 1] = '\0';
    status->type = pet_config.type;
    status->emotion = pet_config.emotion.primary;
    status->emotion_intensity = pet_config.emotion.intensity;
    status->health = pet_config.attributes.health;
    status->happiness = pet_config.attributes.happiness;
    status->level = pet_config.level;
    status->experience = pet_config.experience;
    status->enabled = pet_config.enabled;

    return ESP_OK;
}
