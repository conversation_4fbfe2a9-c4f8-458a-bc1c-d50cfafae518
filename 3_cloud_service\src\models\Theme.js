/**
 * 主题模型
 * <AUTHOR> Team
 * @version 1.0.0
 */

const mongoose = require('mongoose');

const themeSchema = new mongoose.Schema({
  // 主题名称
  name: {
    type: String,
    required: true,
    maxlength: 100,
    index: true
  },
  
  // 主题描述
  description: {
    type: String,
    required: true,
    maxlength: 500
  },
  
  // 作者
  author: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  // 主题分类
  category: {
    type: String,
    required: true,
    enum: ['classic', 'modern', 'cartoon', 'nature', 'tech', 'minimal', 'custom'],
    index: true
  },
  
  // 标签
  tags: [{
    type: String,
    maxlength: 30
  }],
  
  // 版本号
  version: {
    type: String,
    required: true,
    default: '1.0.0'
  },
  
  // 兼容性
  compatibility: [{
    type: String // 支持的固件版本
  }],
  
  // 主题预览图
  preview: {
    thumbnail: String, // 缩略图URL
    screenshots: [String], // 截图URL数组
    video: String // 预览视频URL
  },
  
  // 文件信息
  filePath: {
    type: String,
    required: true
  },
  
  fileName: {
    type: String,
    required: true
  },
  
  fileSize: {
    type: Number,
    required: true
  },
  
  fileHash: {
    type: String // 文件MD5哈希
  },
  
  // 主题配置
  config: {
    // 显示配置
    display: {
      resolution: { type: String, default: '480x480' },
      colorDepth: { type: Number, default: 16 },
      frameRate: { type: Number, default: 30 }
    },
    
    // 动画配置
    animations: {
      enabled: { type: Boolean, default: true },
      duration: { type: Number, default: 300 },
      easing: { type: String, default: 'ease-in-out' }
    },
    
    // 字体配置
    fonts: [{
      name: String,
      size: Number,
      weight: String,
      color: String
    }],
    
    // 颜色主题
    colors: {
      primary: { type: String, default: '#007AFF' },
      secondary: { type: String, default: '#5856D6' },
      background: { type: String, default: '#FFFFFF' },
      text: { type: String, default: '#000000' },
      accent: { type: String, default: '#FF3B30' }
    },
    
    // 布局配置
    layout: {
      clockPosition: { type: String, default: 'center' },
      widgetLayout: { type: String, default: 'grid' },
      spacing: { type: Number, default: 10 }
    }
  },
  
  // 主题资源
  resources: {
    images: [String], // 图片资源列表
    sounds: [String], // 音效资源列表
    fonts: [String],  // 字体资源列表
    animations: [String] // 动画资源列表
  },
  
  // 统计信息
  stats: {
    views: { type: Number, default: 0 },
    downloads: { type: Number, default: 0 },
    rating: { type: Number, default: 0, min: 0, max: 5 },
    ratingCount: { type: Number, default: 0 }
  },
  
  // 用户互动
  likes: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  
  favorites: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  
  // 评价和评论
  reviews: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    rating: {
      type: Number,
      required: true,
      min: 1,
      max: 5
    },
    comment: {
      type: String,
      maxlength: 500
    },
    createdAt: {
      type: Date,
      default: Date.now
    },
    updatedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // 主题状态
  status: {
    type: String,
    enum: ['draft', 'pending', 'published', 'rejected', 'archived'],
    default: 'draft',
    index: true
  },
  
  // 审核信息
  review: {
    reviewer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    reviewedAt: Date,
    comments: String,
    reason: String // 拒绝原因
  },
  
  // 是否精选
  featured: {
    type: Boolean,
    default: false,
    index: true
  },
  
  // 价格信息（如果是付费主题）
  pricing: {
    type: { type: String, enum: ['free', 'paid'], default: 'free' },
    price: { type: Number, default: 0 },
    currency: { type: String, default: 'CNY' }
  },
  
  // 许可证信息
  license: {
    type: String,
    enum: ['MIT', 'GPL', 'Apache', 'Creative Commons', 'Proprietary'],
    default: 'MIT'
  },
  
  // 更新日志
  changelog: [{
    version: String,
    changes: [String],
    date: { type: Date, default: Date.now }
  }],
  
  // 依赖项
  dependencies: [{
    name: String,
    version: String,
    required: { type: Boolean, default: true }
  }],
  
  // 安装统计
  installations: [{
    deviceId: String,
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    installedAt: { type: Date, default: Date.now },
    version: String
  }]
}, {
  timestamps: true,
  collection: 'themes'
});

// 索引
themeSchema.index({ category: 1, status: 1, featured: -1 });
themeSchema.index({ 'stats.rating': -1, 'stats.downloads': -1 });
themeSchema.index({ tags: 1 });
themeSchema.index({ createdAt: -1 });

// 虚拟字段：点赞数
themeSchema.virtual('likesCount').get(function() {
  return this.likes ? this.likes.length : 0;
});

// 虚拟字段：收藏数
themeSchema.virtual('favoritesCount').get(function() {
  return this.favorites ? this.favorites.length : 0;
});

// 虚拟字段：评论数
themeSchema.virtual('reviewsCount').get(function() {
  return this.reviews ? this.reviews.length : 0;
});

// 虚拟字段：文件大小（格式化）
themeSchema.virtual('formattedFileSize').get(function() {
  const size = this.fileSize;
  if (size < 1024) return `${size} B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
  return `${(size / (1024 * 1024)).toFixed(1)} MB`;
});

// 静态方法：获取热门主题
themeSchema.statics.getPopular = function(limit = 10) {
  return this.find({ status: 'published' })
    .sort({ 'stats.downloads': -1, 'stats.rating': -1 })
    .limit(limit)
    .populate('author', 'username avatar');
};

// 静态方法：获取精选主题
themeSchema.statics.getFeatured = function(limit = 10) {
  return this.find({ status: 'published', featured: true })
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate('author', 'username avatar');
};

// 静态方法：获取最新主题
themeSchema.statics.getLatest = function(limit = 10) {
  return this.find({ status: 'published' })
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate('author', 'username avatar');
};

// 静态方法：搜索主题
themeSchema.statics.search = function(query, options = {}) {
  const {
    category,
    tags,
    minRating = 0,
    sort = 'relevance',
    limit = 20,
    skip = 0
  } = options;

  const searchQuery = {
    status: 'published',
    $or: [
      { name: { $regex: query, $options: 'i' } },
      { description: { $regex: query, $options: 'i' } },
      { tags: { $in: [new RegExp(query, 'i')] } }
    ]
  };

  if (category) searchQuery.category = category;
  if (tags && tags.length > 0) searchQuery.tags = { $in: tags };
  if (minRating > 0) searchQuery['stats.rating'] = { $gte: minRating };

  let sortOptions = {};
  switch (sort) {
    case 'downloads':
      sortOptions = { 'stats.downloads': -1 };
      break;
    case 'rating':
      sortOptions = { 'stats.rating': -1 };
      break;
    case 'newest':
      sortOptions = { createdAt: -1 };
      break;
    case 'oldest':
      sortOptions = { createdAt: 1 };
      break;
    default:
      sortOptions = { 'stats.rating': -1, 'stats.downloads': -1 };
  }

  return this.find(searchQuery)
    .sort(sortOptions)
    .limit(limit)
    .skip(skip)
    .populate('author', 'username avatar');
};

// 实例方法：检查用户是否点赞
themeSchema.methods.isLikedBy = function(userId) {
  return this.likes.includes(userId);
};

// 实例方法：检查用户是否收藏
themeSchema.methods.isFavoritedBy = function(userId) {
  return this.favorites.includes(userId);
};

// 实例方法：添加安装记录
themeSchema.methods.addInstallation = function(deviceId, userId, version) {
  this.installations.push({
    deviceId,
    userId,
    version: version || this.version,
    installedAt: new Date()
  });
  
  this.stats.downloads += 1;
  return this.save();
};

// 实例方法：更新评分
themeSchema.methods.updateRating = function() {
  if (this.reviews.length === 0) {
    this.stats.rating = 0;
    this.stats.ratingCount = 0;
  } else {
    const totalRating = this.reviews.reduce((sum, review) => sum + review.rating, 0);
    this.stats.rating = totalRating / this.reviews.length;
    this.stats.ratingCount = this.reviews.length;
  }
  
  return this.save();
};

// 中间件：保存前处理
themeSchema.pre('save', function(next) {
  // 自动生成文件哈希（如果需要）
  if (this.isModified('filePath') && !this.fileHash) {
    // TODO: 生成文件MD5哈希
  }
  
  next();
});

// 中间件：删除前清理
themeSchema.pre('remove', function(next) {
  // 删除主题文件
  // TODO: 删除文件系统中的主题文件
  next();
});

module.exports = mongoose.model('Theme', themeSchema);
