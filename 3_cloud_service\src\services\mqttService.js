/**
 * MQTT服务
 * <AUTHOR> Team
 * @version 1.0.0
 */

const mqtt = require('mqtt');
const EventEmitter = require('events');
const logger = require('../utils/logger');
const DeviceData = require('../models/DeviceData');
const SensorData = require('../models/SensorData');

class MQTTService extends EventEmitter {
  constructor() {
    super();
    this.client = null;
    this.isConnected = false;
    this.deviceTopics = new Map(); // 设备主题映射
    this.subscriptions = new Map(); // 订阅管理
    this.messageHandlers = new Map(); // 消息处理器
    
    this.config = {
      brokerUrl: process.env.MQTT_BROKER_URL || 'mqtt://localhost:1883',
      username: process.env.MQTT_USERNAME || 'timo',
      password: process.env.MQTT_PASSWORD || 'timo123',
      clientId: process.env.MQTT_CLIENT_ID || 'timo-cloud-service',
      keepalive: 60,
      reconnectPeriod: 5000,
      connectTimeout: 30000
    };
    
    this.initializeHandlers();
  }

  /**
   * 初始化消息处理器
   */
  initializeHandlers() {
    // 设备数据上报
    this.messageHandlers.set('device/+/data/sensor', this.handleSensorData.bind(this));
    this.messageHandlers.set('device/+/data/status', this.handleDeviceStatus.bind(this));
    this.messageHandlers.set('device/+/data/heartbeat', this.handleHeartbeat.bind(this));
    
    // 设备控制响应
    this.messageHandlers.set('device/+/response/command', this.handleCommandResponse.bind(this));
    this.messageHandlers.set('device/+/response/config', this.handleConfigResponse.bind(this));
    
    // 设备事件
    this.messageHandlers.set('device/+/event/online', this.handleDeviceOnline.bind(this));
    this.messageHandlers.set('device/+/event/offline', this.handleDeviceOffline.bind(this));
    this.messageHandlers.set('device/+/event/error', this.handleDeviceError.bind(this));
    
    // 语音交互
    this.messageHandlers.set('device/+/voice/asr', this.handleVoiceASR.bind(this));
    this.messageHandlers.set('device/+/voice/tts', this.handleVoiceTTS.bind(this));
    
    // 任务管理
    this.messageHandlers.set('device/+/task/create', this.handleTaskCreate.bind(this));
    this.messageHandlers.set('device/+/task/update', this.handleTaskUpdate.bind(this));
    this.messageHandlers.set('device/+/task/complete', this.handleTaskComplete.bind(this));
  }

  /**
   * 连接MQTT代理
   */
  async connect() {
    try {
      logger.info('连接MQTT代理...', this.config.brokerUrl);
      
      this.client = mqtt.connect(this.config.brokerUrl, {
        clientId: this.config.clientId,
        username: this.config.username,
        password: this.config.password,
        keepalive: this.config.keepalive,
        reconnectPeriod: this.config.reconnectPeriod,
        connectTimeout: this.config.connectTimeout,
        clean: true
      });

      this.client.on('connect', this.onConnect.bind(this));
      this.client.on('message', this.onMessage.bind(this));
      this.client.on('error', this.onError.bind(this));
      this.client.on('close', this.onClose.bind(this));
      this.client.on('reconnect', this.onReconnect.bind(this));

      return new Promise((resolve, reject) => {
        this.client.once('connect', resolve);
        this.client.once('error', reject);
      });
    } catch (error) {
      logger.error('MQTT连接失败:', error);
      throw error;
    }
  }

  /**
   * 连接成功处理
   */
  onConnect() {
    this.isConnected = true;
    logger.info('MQTT连接成功');
    
    // 订阅所有设备主题
    this.subscribeToAllTopics();
    
    this.emit('connected');
  }

  /**
   * 消息接收处理
   */
  onMessage(topic, message) {
    try {
      const messageStr = message.toString();
      logger.debug(`收到MQTT消息: ${topic} -> ${messageStr}`);
      
      // 解析消息
      let payload;
      try {
        payload = JSON.parse(messageStr);
      } catch (e) {
        payload = { raw: messageStr };
      }
      
      // 查找匹配的处理器
      for (const [pattern, handler] of this.messageHandlers) {
        if (this.topicMatches(topic, pattern)) {
          const deviceId = this.extractDeviceId(topic);
          handler(deviceId, payload, topic);
          break;
        }
      }
      
      this.emit('message', topic, payload);
    } catch (error) {
      logger.error('处理MQTT消息失败:', error);
    }
  }

  /**
   * 错误处理
   */
  onError(error) {
    logger.error('MQTT错误:', error);
    this.emit('error', error);
  }

  /**
   * 连接关闭处理
   */
  onClose() {
    this.isConnected = false;
    logger.warn('MQTT连接关闭');
    this.emit('disconnected');
  }

  /**
   * 重连处理
   */
  onReconnect() {
    logger.info('MQTT重连中...');
    this.emit('reconnecting');
  }

  /**
   * 订阅所有主题
   */
  subscribeToAllTopics() {
    const topics = [
      'device/+/data/+',
      'device/+/response/+',
      'device/+/event/+',
      'device/+/voice/+',
      'device/+/task/+'
    ];

    topics.forEach(topic => {
      this.client.subscribe(topic, (err) => {
        if (err) {
          logger.error(`订阅主题失败: ${topic}`, err);
        } else {
          logger.info(`订阅主题成功: ${topic}`);
        }
      });
    });
  }

  /**
   * 主题匹配检查
   */
  topicMatches(topic, pattern) {
    const topicParts = topic.split('/');
    const patternParts = pattern.split('/');
    
    if (topicParts.length !== patternParts.length) {
      return false;
    }
    
    for (let i = 0; i < patternParts.length; i++) {
      if (patternParts[i] !== '+' && patternParts[i] !== topicParts[i]) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * 从主题中提取设备ID
   */
  extractDeviceId(topic) {
    const parts = topic.split('/');
    return parts[1]; // device/{deviceId}/...
  }

  /**
   * 发布消息到设备
   */
  publishToDevice(deviceId, type, command, payload = {}) {
    if (!this.isConnected) {
      throw new Error('MQTT未连接');
    }

    const topic = `device/${deviceId}/${type}/${command}`;
    const message = JSON.stringify({
      timestamp: new Date().toISOString(),
      ...payload
    });

    this.client.publish(topic, message, (err) => {
      if (err) {
        logger.error(`发布消息失败: ${topic}`, err);
      } else {
        logger.debug(`发布消息成功: ${topic} -> ${message}`);
      }
    });
  }

  /**
   * 处理传感器数据
   */
  async handleSensorData(deviceId, payload) {
    try {
      const { sensorType, value, unit, timestamp } = payload;
      
      // 查找设备信息
      const device = await DeviceData.findOne({ deviceId });
      if (!device) {
        logger.warn(`未知设备上报数据: ${deviceId}`);
        return;
      }

      // 保存传感器数据
      const sensorData = new SensorData({
        deviceId,
        userId: device.userId,
        sensorType,
        value,
        unit: unit || '',
        timestamp: timestamp ? new Date(timestamp) : new Date()
      });

      await sensorData.save();
      
      // 发送实时数据更新事件
      this.emit('sensorData', {
        deviceId,
        sensorType,
        value,
        unit,
        timestamp: sensorData.timestamp
      });

      logger.debug(`保存传感器数据: ${deviceId} ${sensorType}=${value}${unit}`);
    } catch (error) {
      logger.error('处理传感器数据失败:', error);
    }
  }

  /**
   * 处理设备状态
   */
  async handleDeviceStatus(deviceId, payload) {
    try {
      const { status, metadata } = payload;
      
      const device = await DeviceData.findOne({ deviceId });
      if (device) {
        await device.updateStatus(status, new Date());
        
        if (metadata) {
          Object.assign(device.stats, metadata);
          await device.save();
        }
        
        this.emit('deviceStatus', { deviceId, status, metadata });
      }
    } catch (error) {
      logger.error('处理设备状态失败:', error);
    }
  }

  /**
   * 处理心跳
   */
  async handleHeartbeat(deviceId, payload) {
    try {
      const device = await DeviceData.findOne({ deviceId });
      if (device) {
        device.lastOnlineAt = new Date();
        if (device.status !== 'online') {
          device.status = 'online';
        }
        
        // 更新统计信息
        if (payload.uptime) {
          device.stats.uptime = payload.uptime;
        }
        
        await device.save();
        this.emit('deviceHeartbeat', { deviceId, timestamp: new Date() });
      }
    } catch (error) {
      logger.error('处理设备心跳失败:', error);
    }
  }

  /**
   * 处理设备上线
   */
  async handleDeviceOnline(deviceId, payload) {
    try {
      const device = await DeviceData.findOne({ deviceId });
      if (device) {
        await device.updateStatus('online');
        this.emit('deviceOnline', { deviceId, timestamp: new Date() });
        logger.info(`设备上线: ${deviceId}`);
      }
    } catch (error) {
      logger.error('处理设备上线失败:', error);
    }
  }

  /**
   * 处理设备离线
   */
  async handleDeviceOffline(deviceId, payload) {
    try {
      const device = await DeviceData.findOne({ deviceId });
      if (device) {
        await device.updateStatus('offline');
        this.emit('deviceOffline', { deviceId, timestamp: new Date() });
        logger.info(`设备离线: ${deviceId}`);
      }
    } catch (error) {
      logger.error('处理设备离线失败:', error);
    }
  }

  /**
   * 处理设备错误
   */
  async handleDeviceError(deviceId, payload) {
    try {
      const { error, code, message } = payload;
      
      const device = await DeviceData.findOne({ deviceId });
      if (device) {
        device.stats.errorCount += 1;
        await device.save();
        
        this.emit('deviceError', { deviceId, error, code, message, timestamp: new Date() });
        logger.warn(`设备错误: ${deviceId} - ${message}`);
      }
    } catch (error) {
      logger.error('处理设备错误失败:', error);
    }
  }

  /**
   * 处理语音ASR请求
   */
  async handleVoiceASR(deviceId, payload) {
    try {
      // TODO: 集成语音识别服务
      this.emit('voiceASR', { deviceId, payload });
    } catch (error) {
      logger.error('处理语音ASR失败:', error);
    }
  }

  /**
   * 处理语音TTS请求
   */
  async handleVoiceTTS(deviceId, payload) {
    try {
      // TODO: 集成语音合成服务
      this.emit('voiceTTS', { deviceId, payload });
    } catch (error) {
      logger.error('处理语音TTS失败:', error);
    }
  }

  /**
   * 处理任务创建
   */
  async handleTaskCreate(deviceId, payload) {
    try {
      // TODO: 集成任务管理服务
      this.emit('taskCreate', { deviceId, payload });
    } catch (error) {
      logger.error('处理任务创建失败:', error);
    }
  }

  /**
   * 处理任务更新
   */
  async handleTaskUpdate(deviceId, payload) {
    try {
      // TODO: 集成任务管理服务
      this.emit('taskUpdate', { deviceId, payload });
    } catch (error) {
      logger.error('处理任务更新失败:', error);
    }
  }

  /**
   * 处理任务完成
   */
  async handleTaskComplete(deviceId, payload) {
    try {
      // TODO: 集成任务管理服务
      this.emit('taskComplete', { deviceId, payload });
    } catch (error) {
      logger.error('处理任务完成失败:', error);
    }
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.client) {
      this.client.end();
      this.isConnected = false;
      logger.info('MQTT连接已断开');
    }
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return {
      connected: this.isConnected,
      clientId: this.config.clientId,
      brokerUrl: this.config.brokerUrl,
      subscriptions: Array.from(this.subscriptions.keys())
    };
  }
}

module.exports = new MQTTService();
