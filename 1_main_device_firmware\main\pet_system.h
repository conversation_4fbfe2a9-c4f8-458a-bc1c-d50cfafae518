/**
 * 虚拟宠物系统头文件
 * <AUTHOR> Team
 * @version 1.0.0
 */

#ifndef PET_SYSTEM_H
#define PET_SYSTEM_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"

#ifdef __cplusplus
extern "C" {
#endif

// 宠物类型枚举
typedef enum {
    PET_TYPE_CAT = 0,
    PET_TYPE_DOG,
    PET_TYPE_RABBIT,
    PET_TYPE_BIRD,
    PET_TYPE_DRAGON,
    PET_TYPE_CUSTOM,
    PET_TYPE_MAX
} pet_type_t;

// 宠物情感枚举
typedef enum {
    PET_EMOTION_HAPPY = 0,
    PET_EMOTION_SAD,
    PET_EMOTION_EXCITED,
    PET_EMOTION_CALM,
    PET_EMOTION_ANGRY,
    PET_EMOTION_SCARED,
    PET_EMOTION_CURIOUS,
    PET_EMOTION_SLEEPY,
    PET_EMOTION_PLAYFUL,
    PET_EMOTION_MAX
} pet_emotion_t;

// 宠物动画枚举
typedef enum {
    PET_ANIM_IDLE = 0,
    PET_ANIM_WALK,
    PET_ANIM_RUN,
    PET_ANIM_JUMP,
    PET_ANIM_SLEEP,
    PET_ANIM_EAT,
    PET_ANIM_PLAY,
    PET_ANIM_HAPPY,
    PET_ANIM_SAD,
    PET_ANIM_EXCITED,
    PET_ANIM_SCARED,
    PET_ANIM_COMFORT,
    PET_ANIM_SHRINK,
    PET_ANIM_GUIDE,
    PET_ANIM_MAX
} pet_animation_t;

// 宠物状态枚举
typedef enum {
    PET_STATE_IDLE = 0,
    PET_STATE_PLAYING,
    PET_STATE_SLEEPING,
    PET_STATE_EATING,
    PET_STATE_LEARNING,
    PET_STATE_SICK,
    PET_STATE_EXCITED,
    PET_STATE_HELPING,
    PET_STATE_MAX
} pet_state_t;

// 宠物交互类型
typedef enum {
    PET_INTERACT_VOICE = 0,
    PET_INTERACT_TOUCH,
    PET_INTERACT_GESTURE,
    PET_INTERACT_TASK,
    PET_INTERACT_PLAY,
    PET_INTERACT_FEED,
    PET_INTERACT_CLEAN,
    PET_INTERACT_MAX
} pet_interaction_t;

// 宠物位置结构
typedef struct {
    int16_t x;
    int16_t y;
} pet_position_t;

// 宠物外观配置
typedef struct {
    uint32_t primary_color;     // 主色调
    uint32_t secondary_color;   // 次色调
    uint32_t eye_color;         // 眼睛颜色
    uint8_t texture;            // 纹理类型
    uint8_t accessories;        // 配饰
} pet_appearance_t;

// 宠物属性
typedef struct {
    uint8_t health;         // 健康值 (0-100)
    uint8_t happiness;      // 快乐值 (0-100)
    uint8_t hunger;         // 饥饿值 (0-100)
    uint8_t fatigue;        // 疲劳值 (0-100)
    uint8_t cleanliness;    // 清洁度 (0-100)
    uint8_t intelligence;   // 智力值 (0-100)
} pet_attributes_t;

// 宠物情感状态
typedef struct {
    pet_emotion_t primary;      // 主要情感
    uint8_t intensity;          // 情感强度 (0-100)
    uint32_t last_update;       // 最后更新时间
} pet_emotion_state_t;

// 宠物显示状态
typedef struct {
    pet_position_t position;    // 位置
    pet_animation_t animation;  // 当前动画
    uint8_t size;              // 大小 (20-100)
    bool visible;              // 是否可见
    uint8_t alpha;             // 透明度 (0-255)
} pet_display_t;

// 宠物配置
typedef struct {
    char name[32];                  // 宠物名称
    pet_type_t type;               // 宠物类型
    pet_appearance_t appearance;    // 外观配置
    pet_attributes_t attributes;    // 属性
    pet_emotion_state_t emotion;    // 情感状态
    pet_display_t display;          // 显示状态
    pet_state_t state;             // 当前状态
    uint16_t level;                // 等级
    uint32_t experience;           // 经验值
    bool enabled;                  // 是否启用
} pet_config_t;

// 宠物事件结构
typedef struct {
    pet_interaction_t type;     // 交互类型
    char content[128];          // 交互内容
    pet_emotion_t user_emotion; // 用户情感
    uint32_t duration;          // 持续时间
    uint32_t timestamp;         // 时间戳
} pet_event_t;

// 宠物动画帧结构
typedef struct {
    uint16_t frame_count;       // 帧数
    uint16_t frame_delay;       // 帧延迟(ms)
    bool loop;                  // 是否循环
    const uint8_t* frames[];    // 帧数据指针数组
} pet_animation_data_t;

// 宠物系统回调函数类型
typedef void (*pet_emotion_callback_t)(pet_emotion_t emotion, uint8_t intensity);
typedef void (*pet_animation_callback_t)(pet_animation_t animation);
typedef void (*pet_interaction_callback_t)(pet_interaction_t type, const char* content);

// 宠物系统配置
typedef struct {
    pet_emotion_callback_t emotion_callback;
    pet_animation_callback_t animation_callback;
    pet_interaction_callback_t interaction_callback;
    uint16_t update_interval_ms;    // 更新间隔
    uint16_t animation_fps;         // 动画帧率
    bool auto_emotion_update;       // 自动情感更新
} pet_system_config_t;

// 函数声明

/**
 * 初始化宠物系统
 */
esp_err_t pet_system_init(const pet_system_config_t* config);

/**
 * 反初始化宠物系统
 */
esp_err_t pet_system_deinit(void);

/**
 * 设置宠物配置
 */
esp_err_t pet_set_config(const pet_config_t* config);

/**
 * 获取宠物配置
 */
esp_err_t pet_get_config(pet_config_t* config);

/**
 * 更新宠物情感
 */
esp_err_t pet_update_emotion(pet_emotion_t emotion, uint8_t intensity);

/**
 * 设置宠物动画
 */
esp_err_t pet_set_animation(pet_animation_t animation);

/**
 * 设置宠物位置
 */
esp_err_t pet_set_position(int16_t x, int16_t y);

/**
 * 设置宠物大小
 */
esp_err_t pet_set_size(uint8_t size);

/**
 * 设置宠物可见性
 */
esp_err_t pet_set_visibility(bool visible);

/**
 * 处理宠物交互
 */
esp_err_t pet_process_interaction(const pet_event_t* event);

/**
 * 更新宠物属性
 */
esp_err_t pet_update_attributes(const pet_attributes_t* attributes);

/**
 * 获取宠物状态
 */
esp_err_t pet_get_status(pet_state_t* state, pet_emotion_t* emotion);

/**
 * 宠物表情表达
 */
esp_err_t pet_express_emotion(pet_emotion_t emotion, uint16_t duration_ms);

/**
 * 宠物任务模式（缩小并引导）
 */
esp_err_t pet_enter_task_mode(bool enable);

/**
 * 宠物睡眠模式
 */
esp_err_t pet_enter_sleep_mode(bool enable);

/**
 * 宠物播放声音
 */
esp_err_t pet_play_sound(const char* sound_name);

/**
 * 宠物说话（TTS）
 */
esp_err_t pet_speak(const char* text);

/**
 * 获取宠物动画数据
 */
const pet_animation_data_t* pet_get_animation_data(pet_animation_t animation);

/**
 * 宠物系统任务
 */
void pet_system_task(void* pvParameters);

/**
 * 宠物渲染任务
 */
void pet_render_task(void* pvParameters);

/**
 * 宠物情感更新任务
 */
void pet_emotion_task(void* pvParameters);

#ifdef __cplusplus
}
#endif

#endif // PET_SYSTEM_H
