<!--主题管理页面-->
<view class="theme-page">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-title">主题商店</view>
    <view class="header-actions">
      <view class="action-btn" bindtap="onShowSearch">
        <van-icon name="search" size="20px" />
      </view>
      <view class="action-btn" bindtap="onCreateTheme">
        <van-icon name="plus" size="20px" />
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-bar" wx:if="{{showSearch}}">
    <van-search
      value="{{searchKeyword}}"
      placeholder="搜索主题"
      bind:search="onSearch"
      bind:cancel="onHideSearch"
      bind:change="onSearchInput"
      show-action
    />
  </view>

  <!-- 分类标签 -->
  <view class="category-tabs">
    <scroll-view scroll-x class="tabs-scroll">
      <view class="tabs-container">
        <view 
          class="tab-item {{currentCategory === item.id ? 'active' : ''}}"
          wx:for="{{categories}}"
          wx:key="id"
          data-category="{{item.id}}"
          bindtap="onCategoryChange"
        >
          <van-icon name="{{item.icon}}" size="16px" />
          <text>{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 主题列表 -->
  <view class="theme-list" wx:if="{{!loading}}">
    <scroll-view 
      scroll-y 
      class="list-scroll"
      enable-back-to-top
      refresher-enabled
      refresher-triggered="{{refreshing}}"
      bindrefresherrefresh="onPullDownRefresh"
    >
      <view class="themes-grid">
        <view 
          class="theme-card"
          wx:for="{{themes}}"
          wx:key="id"
          data-theme="{{item}}"
          bindtap="onPreviewTheme"
        >
          <!-- 主题预览图 -->
          <view class="theme-preview">
            <image src="{{item.preview}}" mode="aspectFill" />
            
            <!-- 当前主题标识 -->
            <view class="current-badge" wx:if="{{currentTheme && currentTheme.id === item.id}}">
              <van-icon name="success" size="12px" color="#fff" />
              <text>当前</text>
            </view>
            
            <!-- 主题类型标识 -->
            <view class="type-badge {{item.category}}">
              {{item.category === 'official' ? '官方' : item.category === 'community' ? '社区' : '我的'}}
            </view>
          </view>
          
          <!-- 主题信息 -->
          <view class="theme-info">
            <view class="theme-name">{{item.name}}</view>
            <view class="theme-meta">
              <view class="author">{{item.author}}</view>
              <view class="stats">
                <van-icon name="star" size="12px" />
                <text>{{item.rating}}</text>
                <van-icon name="eye" size="12px" />
                <text>{{item.downloads}}</text>
              </view>
            </view>
          </view>
          
          <!-- 操作按钮 -->
          <view class="theme-actions" catchtap="stopPropagation">
            <!-- 应用按钮 -->
            <view 
              class="action-btn apply-btn"
              wx:if="{{!currentTheme || currentTheme.id !== item.id}}"
              data-theme="{{item}}"
              bindtap="onApplyTheme"
            >
              应用
            </view>
            
            <!-- 下载按钮 -->
            <view 
              class="action-btn download-btn"
              wx:if="{{!item.downloaded}}"
              data-theme="{{item}}"
              bindtap="onDownloadTheme"
            >
              <van-icon name="down" size="14px" />
            </view>
            
            <!-- 编辑按钮 -->
            <view 
              class="action-btn edit-btn"
              wx:if="{{item.category === 'my'}}"
              data-theme="{{item}}"
              bindtap="onEditTheme"
            >
              <van-icon name="edit" size="14px" />
            </view>
            
            <!-- 删除按钮 -->
            <view 
              class="action-btn delete-btn"
              wx:if="{{item.category === 'my'}}"
              data-theme="{{item}}"
              bindtap="onDeleteTheme"
            >
              <van-icon name="delete" size="14px" />
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{themes.length === 0}}">
        <van-empty description="暂无主题" />
      </view>
    </scroll-view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <van-loading type="spinner" size="24px" />
    <text>加载中...</text>
  </view>
</view>

<!-- 浮动创建按钮 -->
<view class="fab-button" bindtap="onCreateTheme">
  <van-icon name="plus" size="24px" color="#fff" />
</view>
