/**
 * 传感器数据模型
 * <AUTHOR> Team
 * @version 1.0.0
 */

const mongoose = require('mongoose');

const sensorDataSchema = new mongoose.Schema({
  // 设备ID
  deviceId: {
    type: String,
    required: true,
    index: true
  },
  
  // 用户ID
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  // 传感器类型
  sensorType: {
    type: String,
    required: true,
    enum: [
      'temperature',    // 温度
      'humidity',       // 湿度
      'co2',           // CO2浓度
      'light',         // 光照强度
      'sound',         // 声音强度
      'motion',        // 运动检测
      'battery',       // 电池电量
      'charging'       // 充电状态
    ],
    index: true
  },
  
  // 传感器数值
  value: {
    type: Number,
    required: true
  },
  
  // 单位
  unit: {
    type: String,
    default: ''
  },
  
  // 数据质量标识
  quality: {
    type: String,
    enum: ['good', 'fair', 'poor'],
    default: 'good'
  },
  
  // 原始数据
  rawData: {
    type: mongoose.Schema.Types.Mixed
  },
  
  // 时间戳
  timestamp: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  // 数据来源
  source: {
    type: String,
    enum: ['device', 'manual', 'calculated'],
    default: 'device'
  },
  
  // 位置信息（可选）
  location: {
    latitude: Number,
    longitude: Number,
    altitude: Number
  },
  
  // 环境信息
  environment: {
    room: String,
    floor: String,
    building: String
  },
  
  // 标签
  tags: [{
    type: String
  }],
  
  // 备注
  notes: {
    type: String,
    maxlength: 500
  }
}, {
  timestamps: true,
  // 设置集合名称
  collection: 'sensor_data'
});

// 复合索引
sensorDataSchema.index({ deviceId: 1, sensorType: 1, timestamp: -1 });
sensorDataSchema.index({ userId: 1, timestamp: -1 });
sensorDataSchema.index({ deviceId: 1, timestamp: -1 });

// 虚拟字段：格式化时间
sensorDataSchema.virtual('formattedTimestamp').get(function() {
  return this.timestamp.toISOString();
});

// 虚拟字段：数据年龄（分钟）
sensorDataSchema.virtual('ageInMinutes').get(function() {
  return Math.floor((Date.now() - this.timestamp.getTime()) / (1000 * 60));
});

// 静态方法：获取设备最新数据
sensorDataSchema.statics.getLatestByDevice = function(deviceId, sensorTypes = []) {
  const match = { deviceId };
  if (sensorTypes.length > 0) {
    match.sensorType = { $in: sensorTypes };
  }
  
  return this.aggregate([
    { $match: match },
    { $sort: { timestamp: -1 } },
    {
      $group: {
        _id: '$sensorType',
        latestData: { $first: '$$ROOT' }
      }
    },
    { $replaceRoot: { newRoot: '$latestData' } }
  ]);
};

// 静态方法：获取时间范围内的统计数据
sensorDataSchema.statics.getStatistics = function(deviceId, sensorType, startTime, endTime) {
  return this.aggregate([
    {
      $match: {
        deviceId,
        sensorType,
        timestamp: { $gte: startTime, $lte: endTime }
      }
    },
    {
      $group: {
        _id: null,
        count: { $sum: 1 },
        avg: { $avg: '$value' },
        min: { $min: '$value' },
        max: { $max: '$value' },
        first: { $first: '$value' },
        last: { $last: '$value' },
        firstTime: { $first: '$timestamp' },
        lastTime: { $last: '$timestamp' }
      }
    }
  ]);
};

// 静态方法：获取趋势数据
sensorDataSchema.statics.getTrend = function(deviceId, sensorType, interval = 'hour', limit = 24) {
  const groupBy = {
    hour: {
      year: { $year: '$timestamp' },
      month: { $month: '$timestamp' },
      day: { $dayOfMonth: '$timestamp' },
      hour: { $hour: '$timestamp' }
    },
    day: {
      year: { $year: '$timestamp' },
      month: { $month: '$timestamp' },
      day: { $dayOfMonth: '$timestamp' }
    },
    week: {
      year: { $year: '$timestamp' },
      week: { $week: '$timestamp' }
    },
    month: {
      year: { $year: '$timestamp' },
      month: { $month: '$timestamp' }
    }
  };

  return this.aggregate([
    {
      $match: {
        deviceId,
        sensorType,
        timestamp: { $gte: new Date(Date.now() - limit * (interval === 'hour' ? 3600000 : interval === 'day' ? 86400000 : interval === 'week' ? 604800000 : 2592000000)) }
      }
    },
    {
      $group: {
        _id: groupBy[interval],
        avg: { $avg: '$value' },
        min: { $min: '$value' },
        max: { $max: '$value' },
        count: { $sum: 1 },
        timestamp: { $first: '$timestamp' }
      }
    },
    { $sort: { '_id': 1 } },
    { $limit: limit }
  ]);
};

// 实例方法：检查数据是否异常
sensorDataSchema.methods.isAnomalous = function(thresholds = {}) {
  const { min, max } = thresholds;
  
  if (min !== undefined && this.value < min) return true;
  if (max !== undefined && this.value > max) return true;
  
  return false;
};

// 实例方法：格式化显示
sensorDataSchema.methods.toDisplayString = function() {
  return `${this.sensorType}: ${this.value}${this.unit} (${this.timestamp.toLocaleString()})`;
};

// 中间件：保存前验证
sensorDataSchema.pre('save', function(next) {
  // 验证数值范围
  const ranges = {
    temperature: { min: -50, max: 100 },
    humidity: { min: 0, max: 100 },
    co2: { min: 0, max: 10000 },
    light: { min: 0, max: 100000 },
    sound: { min: 0, max: 120 },
    battery: { min: 0, max: 100 }
  };
  
  const range = ranges[this.sensorType];
  if (range && (this.value < range.min || this.value > range.max)) {
    this.quality = 'poor';
  }
  
  next();
});

// 中间件：删除前清理
sensorDataSchema.pre('remove', function(next) {
  // 可以在这里添加删除前的清理逻辑
  next();
});

// 导出模型
module.exports = mongoose.model('SensorData', sensorDataSchema);
