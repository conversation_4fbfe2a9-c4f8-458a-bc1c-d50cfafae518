<!--首页-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="content">
    <!-- 用户信息栏 -->
    <view class="user-header">
      <view class="user-info">
        <image class="avatar" src="{{userInfo.avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
        <view class="user-text">
          <text class="greeting">你好，{{userInfo.nickname || '用户'}}</text>
          <text class="time">{{currentTime}}</text>
        </view>
      </view>
      <view class="weather-info">
        <text class="weather">{{weather.temperature}}°</text>
        <text class="weather-desc">{{weather.description}}</text>
      </view>
    </view>

    <!-- 设备状态卡片 -->
    <view class="device-card">
      <view class="card-header">
        <text class="card-title">当前设备</text>
        <text class="more-btn" bindtap="onMoreDevicesTap">更多</text>
      </view>
      
      <view wx:if="{{currentDevice}}" class="device-info">
        <view class="device-main">
          <picker range="{{devices}}" range-key="name" value="{{currentDeviceIndex}}" bindchange="onDeviceChange">
            <view class="device-selector">
              <text class="device-name">{{currentDevice.name}}</text>
              <text class="device-arrow">▼</text>
            </view>
          </picker>
          <view class="device-status {{deviceStatus}}">
            <text class="status-dot"></text>
            <text class="status-text">{{deviceStatus === 'online' ? '在线' : '离线'}}</text>
          </view>
        </view>
        
        <!-- 传感器数据 -->
        <view class="sensor-data">
          <view class="sensor-item">
            <text class="sensor-label">温度</text>
            <text class="sensor-value">{{sensorData.temperature}}°C</text>
          </view>
          <view class="sensor-item">
            <text class="sensor-label">湿度</text>
            <text class="sensor-value">{{sensorData.humidity}}%</text>
          </view>
          <view class="sensor-item">
            <text class="sensor-label">CO₂</text>
            <text class="sensor-value">{{sensorData.co2}}ppm</text>
          </view>
          <view class="sensor-item">
            <text class="sensor-label">舒适度</text>
            <text class="sensor-value">{{sensorData.comfort}}</text>
          </view>
        </view>
      </view>
      
      <view wx:else class="no-device">
        <text class="no-device-text">暂无设备</text>
        <button class="bind-device-btn" bindtap="onBindDeviceTap">绑定设备</button>
      </view>
    </view>

    <!-- 快捷功能 -->
    <view class="quick-actions">
      <view class="card-header">
        <text class="card-title">快捷功能</text>
      </view>
      <view class="actions-grid">
        <view wx:for="{{quickActions}}" wx:key="id" class="action-item" data-url="{{item.url}}" bindtap="onQuickActionTap">
          <image class="action-icon" src="{{item.icon}}" mode="aspectFit"></image>
          <text class="action-name">{{item.name}}</text>
        </view>
      </view>
    </view>

    <!-- 今日任务 -->
    <view class="today-tasks">
      <view class="card-header">
        <text class="card-title">今日任务</text>
        <text class="more-btn" bindtap="onMoreTasksTap">更多</text>
      </view>
      
      <view wx:if="{{todayTasks.length > 0}}" class="task-list">
        <view wx:for="{{todayTasks}}" wx:key="id" class="task-item" data-task-id="{{item.id}}" bindtap="onTaskTap">
          <view class="task-content">
            <view class="task-checkbox {{item.status === 'completed' ? 'checked' : ''}}" data-task-id="{{item.id}}" data-index="{{index}}" catchtap="onCompleteTask">
              <text wx:if="{{item.status === 'completed'}}" class="check-icon">✓</text>
            </view>
            <view class="task-text">
              <text class="task-title {{item.status === 'completed' ? 'completed' : ''}}">{{item.title}}</text>
              <text class="task-time">{{item.dueTime}}</text>
            </view>
          </view>
          <view class="task-priority priority-{{item.priority}}"></view>
        </view>
      </view>
      
      <view wx:else class="no-tasks">
        <text class="no-tasks-text">今日暂无任务</text>
      </view>
      
      <!-- 任务统计 -->
      <view class="task-stats">
        <view class="stat-item">
          <text class="stat-number">{{taskStats.completed}}</text>
          <text class="stat-label">已完成</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{taskStats.pending}}</text>
          <text class="stat-label">待完成</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{taskStats.total}}</text>
          <text class="stat-label">总计</text>
        </view>
      </view>
    </view>

    <!-- 虚拟宠物 -->
    <view wx:if="{{activePet}}" class="pet-card">
      <view class="card-header">
        <text class="card-title">我的宠物</text>
        <text class="more-btn" bindtap="onPetDetailTap">详情</text>
      </view>
      
      <view class="pet-info">
        <view class="pet-avatar" bindtap="onPetDetailTap">
          <image class="pet-image" src="{{activePet.avatar}}" mode="aspectFit"></image>
          <view class="pet-emotion emotion-{{activePet.emotion}}"></view>
        </view>
        
        <view class="pet-details">
          <text class="pet-name">{{activePet.name}}</text>
          <text class="pet-level">Lv.{{activePet.level}}</text>
          
          <!-- 属性条 -->
          <view class="pet-attributes">
            <view class="attribute-item">
              <text class="attr-label">健康</text>
              <view class="attr-bar">
                <view class="attr-fill" style="width: {{activePet.attributes.health}}%"></view>
              </view>
              <text class="attr-value">{{activePet.attributes.health}}</text>
            </view>
            <view class="attribute-item">
              <text class="attr-label">快乐</text>
              <view class="attr-bar">
                <view class="attr-fill" style="width: {{activePet.attributes.happiness}}%"></view>
              </view>
              <text class="attr-value">{{activePet.attributes.happiness}}</text>
            </view>
            <view class="attribute-item">
              <text class="attr-label">精力</text>
              <view class="attr-bar">
                <view class="attr-fill" style="width: {{activePet.attributes.energy}}%"></view>
              </view>
              <text class="attr-value">{{activePet.attributes.energy}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 宠物交互按钮 -->
      <view class="pet-actions">
        <button class="pet-action-btn" data-action="feed" bindtap="onPetInteract">喂食</button>
        <button class="pet-action-btn" data-action="play" bindtap="onPetInteract">玩耍</button>
        <button class="pet-action-btn" data-action="clean" bindtap="onPetInteract">清洁</button>
      </view>
    </view>
  </view>
</view>
