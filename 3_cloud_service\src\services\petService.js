/**
 * 虚拟宠物服务
 * <AUTHOR> Team
 * @version 1.0.0
 */

const Pet = require('../models/Pet');
const logger = require('../utils/logger');

class PetService {
  constructor() {
    this.emotionAnalyzer = new EmotionAnalyzer();
    this.growthManager = new GrowthManager();
    this.interactionProcessor = new InteractionProcessor();
  }

  /**
   * 获取用户宠物
   */
  async getUserPet(userId) {
    try {
      let pet = await Pet.getUserPet(userId);
      
      if (!pet) {
        // 如果没有宠物，创建一个新的
        pet = await this.createPet(userId);
      }
      
      // 检查是否需要日常维护
      await this.checkDailyMaintenance(pet);
      
      return pet;
    } catch (error) {
      logger.error('Get user pet error:', error);
      throw error;
    }
  }

  /**
   * 创建宠物
   */
  async createPet(userId, deviceId, petData = {}) {
    try {
      const pet = await Pet.createPet(userId, deviceId, petData);
      
      logger.info(`Created new pet for user ${userId}: ${pet.name}`);
      
      // 记录创建事件
      await pet.recordInteraction({
        type: 'play',
        content: 'pet_created',
        userEmotion: 'excited',
        petResponse: 'happy_birth',
        duration: 0
      });
      
      return pet;
    } catch (error) {
      logger.error('Create pet error:', error);
      throw error;
    }
  }

  /**
   * 更新宠物信息
   */
  async updatePet(userId, updates) {
    try {
      const pet = await Pet.getUserPet(userId);
      if (!pet) {
        throw new Error('Pet not found');
      }

      // 允许更新的字段
      const allowedFields = ['name', 'appearance', 'settings'];
      const filteredUpdates = {};
      
      allowedFields.forEach(field => {
        if (updates[field] !== undefined) {
          filteredUpdates[field] = updates[field];
        }
      });

      Object.assign(pet, filteredUpdates);
      await pet.save();

      logger.info(`Updated pet for user ${userId}`);
      return pet;
    } catch (error) {
      logger.error('Update pet error:', error);
      throw error;
    }
  }

  /**
   * 处理用户交互
   */
  async processInteraction(userId, interactionData) {
    try {
      const pet = await Pet.getUserPet(userId);
      if (!pet) {
        throw new Error('Pet not found');
      }

      const { type, content, userEmotion, duration } = interactionData;
      
      // 分析用户情感
      const analyzedEmotion = await this.emotionAnalyzer.analyzeUserEmotion(content, userEmotion);
      
      // 生成宠物反应
      const petResponse = await this.generatePetResponse(pet, type, content, analyzedEmotion);
      
      // 更新宠物情感
      await this.updatePetEmotion(pet, type, analyzedEmotion, petResponse);
      
      // 记录交互
      await pet.recordInteraction({
        type,
        content,
        userEmotion: analyzedEmotion.emotion,
        petResponse: petResponse.action,
        duration
      });

      // 更新属性
      await this.updatePetAttributes(pet, type, petResponse);

      return {
        petResponse,
        petEmotion: pet.emotions.primary,
        petStatus: pet.status.current,
        attributes: pet.attributes
      };
    } catch (error) {
      logger.error('Process interaction error:', error);
      throw error;
    }
  }

  /**
   * 生成宠物反应
   */
  async generatePetResponse(pet, interactionType, content, userEmotion) {
    try {
      const responses = {
        voice: await this.generateVoiceResponse(pet, content, userEmotion),
        touch: await this.generateTouchResponse(pet, userEmotion),
        gesture: await this.generateGestureResponse(pet, content, userEmotion),
        task: await this.generateTaskResponse(pet, content),
        play: await this.generatePlayResponse(pet, userEmotion),
        feed: await this.generateFeedResponse(pet),
        clean: await this.generateCleanResponse(pet)
      };

      return responses[interactionType] || responses.voice;
    } catch (error) {
      logger.error('Generate pet response error:', error);
      return { action: 'idle', animation: 'idle', sound: null, text: '...' };
    }
  }

  /**
   * 生成语音交互反应
   */
  async generateVoiceResponse(pet, content, userEmotion) {
    const personality = pet.emotions.personality;
    const currentEmotion = pet.emotions.primary;
    
    // 基于宠物性格和当前情感生成反应
    let responseType = 'neutral';
    let animation = 'idle';
    let sound = null;
    let text = '';

    if (userEmotion.emotion === 'happy') {
      if (personality.extroversion > 0) {
        responseType = 'excited';
        animation = 'jump';
        sound = 'happy_chirp';
        text = '主人看起来很开心呢！我也很开心！';
      } else {
        responseType = 'content';
        animation = 'smile';
        text = '主人开心，我就安心了~';
      }
    } else if (userEmotion.emotion === 'sad') {
      responseType = 'comforting';
      animation = 'comfort';
      sound = 'gentle_purr';
      text = '主人别难过，我会一直陪着你的';
    } else if (userEmotion.emotion === 'angry') {
      if (personality.emotionalStability > 50) {
        responseType = 'calming';
        animation = 'calm';
        text = '主人，深呼吸，我们一起放松一下吧';
      } else {
        responseType = 'scared';
        animation = 'hide';
        text = '主人...是我做错什么了吗？';
      }
    }

    return {
      action: responseType,
      animation,
      sound,
      text,
      emotion: this.mapResponseToEmotion(responseType)
    };
  }

  /**
   * 生成触摸反应
   */
  async generateTouchResponse(pet, userEmotion) {
    const happiness = pet.attributes.happiness;
    
    if (happiness > 70) {
      return {
        action: 'purr',
        animation: 'purr',
        sound: 'content_purr',
        text: '好舒服~',
        emotion: 'happy'
      };
    } else if (happiness < 30) {
      return {
        action: 'reluctant',
        animation: 'shy',
        sound: null,
        text: '我现在不太想被摸...',
        emotion: 'sad'
      };
    } else {
      return {
        action: 'enjoy',
        animation: 'enjoy_touch',
        sound: 'soft_purr',
        text: '嗯~主人的手好温暖',
        emotion: 'calm'
      };
    }
  }

  /**
   * 更新宠物情感
   */
  async updatePetEmotion(pet, interactionType, userEmotion, petResponse) {
    try {
      let emotionChange = 0;
      let newEmotion = pet.emotions.primary;

      // 根据交互类型和用户情感调整宠物情感
      if (userEmotion.emotion === 'happy') {
        emotionChange = 10;
        newEmotion = 'happy';
      } else if (userEmotion.emotion === 'sad') {
        emotionChange = -5;
        newEmotion = 'sad';
      } else if (userEmotion.emotion === 'angry') {
        emotionChange = -15;
        newEmotion = 'scared';
      }

      // 考虑宠物性格特征
      const personality = pet.emotions.personality;
      if (personality.emotionalStability > 50) {
        emotionChange *= 0.7; // 情绪稳定的宠物变化较小
      }

      const newIntensity = Math.max(0, Math.min(100, pet.emotions.intensity + emotionChange));
      
      await pet.updateEmotion(newEmotion, newIntensity, `${interactionType}_${userEmotion.emotion}`);
      
    } catch (error) {
      logger.error('Update pet emotion error:', error);
    }
  }

  /**
   * 更新宠物属性
   */
  async updatePetAttributes(pet, interactionType, petResponse) {
    try {
      const changes = {};

      switch (interactionType) {
        case 'voice':
        case 'touch':
        case 'play':
          changes.happiness = 5;
          changes.fatigue = 2;
          break;
        case 'feed':
          changes.hunger = -30;
          changes.health = 5;
          changes.happiness = 10;
          break;
        case 'clean':
          changes.cleanliness = 40;
          changes.happiness = 8;
          break;
        case 'task':
          changes.intelligence = 2;
          changes.fatigue = 5;
          break;
      }

      if (Object.keys(changes).length > 0) {
        await pet.updateAttributes(changes);
      }
    } catch (error) {
      logger.error('Update pet attributes error:', error);
    }
  }

  /**
   * 检查日常维护
   */
  async checkDailyMaintenance(pet) {
    try {
      const lastUpdate = pet.dailyStatus.lastUpdate;
      const now = new Date();
      const daysSinceUpdate = Math.floor((now - lastUpdate) / (1000 * 60 * 60 * 24));

      if (daysSinceUpdate > 0) {
        for (let i = 0; i < daysSinceUpdate; i++) {
          await pet.dailyMaintenance();
        }
        logger.info(`Performed ${daysSinceUpdate} days of maintenance for pet ${pet._id}`);
      }
    } catch (error) {
      logger.error('Daily maintenance error:', error);
    }
  }

  /**
   * 获取宠物状态
   */
  async getPetStatus(userId) {
    try {
      const pet = await Pet.getUserPet(userId);
      if (!pet) {
        return null;
      }

      return {
        basic: {
          name: pet.name,
          type: pet.type,
          level: pet.lifecycle.level,
          age: pet.ageInDays,
          stage: pet.lifecycle.stage
        },
        attributes: pet.attributes,
        emotion: {
          primary: pet.emotions.primary,
          intensity: pet.emotions.intensity,
          description: pet.emotionDescription
        },
        status: pet.status,
        health: pet.overallHealth,
        interactions: {
          total: pet.interactions.totalCount,
          today: pet.interactions.todayCount,
          last: pet.interactions.lastInteraction
        }
      };
    } catch (error) {
      logger.error('Get pet status error:', error);
      throw error;
    }
  }

  /**
   * 映射反应类型到情感
   */
  mapResponseToEmotion(responseType) {
    const mapping = {
      excited: 'excited',
      content: 'happy',
      comforting: 'calm',
      calming: 'calm',
      scared: 'scared',
      purr: 'happy',
      reluctant: 'sad',
      enjoy: 'happy'
    };
    return mapping[responseType] || 'calm';
  }
}

/**
 * 情感分析器
 */
class EmotionAnalyzer {
  async analyzeUserEmotion(content, providedEmotion) {
    // TODO: 集成真实的情感分析AI模型
    // 这里使用简单的关键词分析
    
    const emotionKeywords = {
      happy: ['开心', '高兴', '快乐', '兴奋', '棒', '好', '喜欢'],
      sad: ['难过', '伤心', '沮丧', '失望', '不开心', '郁闷'],
      angry: ['生气', '愤怒', '烦躁', '讨厌', '气死了', '烦死了'],
      scared: ['害怕', '恐惧', '紧张', '担心', '焦虑'],
      calm: ['平静', '放松', '安静', '舒服', '淡定']
    };

    let detectedEmotion = 'calm';
    let confidence = 0.5;

    if (providedEmotion) {
      detectedEmotion = providedEmotion;
      confidence = 0.8;
    } else if (content) {
      for (const [emotion, keywords] of Object.entries(emotionKeywords)) {
        for (const keyword of keywords) {
          if (content.includes(keyword)) {
            detectedEmotion = emotion;
            confidence = 0.7;
            break;
          }
        }
        if (confidence > 0.6) break;
      }
    }

    return {
      emotion: detectedEmotion,
      confidence,
      intensity: Math.floor(confidence * 100)
    };
  }
}

/**
 * 成长管理器
 */
class GrowthManager {
  calculateGrowthRate(pet) {
    const baseRate = 1;
    const happinessBonus = pet.attributes.happiness > 80 ? 1.2 : 1.0;
    const healthBonus = pet.attributes.health > 80 ? 1.1 : 1.0;
    const interactionBonus = pet.interactions.todayCount > 5 ? 1.3 : 1.0;
    
    return baseRate * happinessBonus * healthBonus * interactionBonus;
  }

  async processGrowth(pet) {
    const growthRate = this.calculateGrowthRate(pet);
    const expGain = Math.floor(growthRate * 10);
    
    await pet.addExperience(expGain);
    return expGain;
  }
}

/**
 * 交互处理器
 */
class InteractionProcessor {
  async processVoiceInteraction(pet, content, userEmotion) {
    // 处理语音交互的特殊逻辑
    return {
      type: 'voice',
      processed: true,
      response: 'voice_response'
    };
  }

  async processTouchInteraction(pet, touchData) {
    // 处理触摸交互的特殊逻辑
    return {
      type: 'touch',
      processed: true,
      response: 'touch_response'
    };
  }
}

module.exports = new PetService();
