/* 主题管理页面样式 */
.theme-page {
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  background: #fff;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #eee;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f8f8f8;
}

/* 搜索栏 */
.search-bar {
  background: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}

/* 分类标签 */
.category-tabs {
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs-container {
  display: flex;
  padding: 20rpx;
  gap: 30rpx;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  padding: 20rpx;
  border-radius: 16rpx;
  min-width: 120rpx;
  transition: all 0.3s;
}

.tab-item.active {
  background: #007AFF;
  color: #fff;
}

.tab-item text {
  font-size: 24rpx;
}

/* 主题列表 */
.theme-list {
  flex: 1;
  overflow: hidden;
}

.list-scroll {
  height: 100%;
}

.themes-grid {
  padding: 30rpx;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
}

/* 主题卡片 */
.theme-card {
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  transition: transform 0.3s;
}

.theme-card:active {
  transform: scale(0.98);
}

.theme-preview {
  position: relative;
  width: 100%;
  height: 240rpx;
}

.theme-preview image {
  width: 100%;
  height: 100%;
}

.current-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: #4CAF50;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.type-badge {
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  color: #fff;
}

.type-badge.official {
  background: #FF9500;
}

.type-badge.community {
  background: #007AFF;
}

.type-badge.my {
  background: #34C759;
}

/* 主题信息 */
.theme-info {
  padding: 20rpx;
}

.theme-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.theme-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.author {
  font-size: 24rpx;
  color: #666;
}

.stats {
  display: flex;
  align-items: center;
  gap: 10rpx;
  font-size: 20rpx;
  color: #999;
}

/* 操作按钮 */
.theme-actions {
  padding: 20rpx;
  display: flex;
  gap: 15rpx;
  border-top: 1rpx solid #f0f0f0;
}

.theme-actions .action-btn {
  flex: 1;
  height: 60rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.apply-btn {
  background: #007AFF;
  color: #fff;
}

.download-btn {
  background: #34C759;
  color: #fff;
}

.edit-btn {
  background: #FF9500;
  color: #fff;
}

.delete-btn {
  background: #FF3B30;
  color: #fff;
}

/* 空状态 */
.empty-state {
  padding: 100rpx;
  text-align: center;
}

/* 加载状态 */
.loading-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  color: #666;
}

/* 浮动按钮 */
.fab-button {
  position: fixed;
  bottom: 100rpx;
  right: 60rpx;
  width: 100rpx;
  height: 100rpx;
  background: #007AFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 30rpx rgba(0,122,255,0.3);
  z-index: 100;
}
