/**
 * API接口管理
 */

const auth = require('./auth');

class API {
  constructor() {
    this.baseURL = '';
    this.timeout = 10000;
  }

  /**
   * 初始化API
   */
  init(baseURL) {
    this.baseURL = baseURL;
  }

  /**
   * 发送请求
   */
  request(options) {
    return new Promise((resolve, reject) => {
      const {
        url,
        method = 'GET',
        data = {},
        header = {},
        needAuth = true
      } = options;

      // 构建完整URL
      const fullURL = url.startsWith('http') ? url : `${this.baseURL}${url}`;

      // 设置请求头
      const requestHeader = {
        'Content-Type': 'application/json',
        ...header
      };

      // 添加认证头
      if (needAuth && auth.getToken()) {
        requestHeader['Authorization'] = `Bearer ${auth.getToken()}`;
      }

      // 发送请求
      wx.request({
        url: fullURL,
        method,
        data,
        header: requestHeader,
        timeout: this.timeout,
        success: (res) => {
          const { statusCode, data: responseData } = res;

          if (statusCode >= 200 && statusCode < 300) {
            resolve(responseData);
          } else if (statusCode === 401) {
            // 认证失败，清除token并跳转登录
            auth.clearToken();
            wx.reLaunch({
              url: '/pages/login/login'
            });
            reject(new Error('认证失败'));
          } else {
            reject(new Error(responseData.message || `请求失败 ${statusCode}`));
          }
        },
        fail: (error) => {
          console.error('请求失败:', error);
          reject(new Error('网络请求失败'));
        }
      });
    });
  }

  /**
   * GET请求
   */
  get(url, params = {}, options = {}) {
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    const fullURL = queryString ? `${url}?${queryString}` : url;
    
    return this.request({
      url: fullURL,
      method: 'GET',
      ...options
    });
  }

  /**
   * POST请求
   */
  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    });
  }

  /**
   * PUT请求
   */
  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    });
  }

  /**
   * DELETE请求
   */
  delete(url, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      ...options
    });
  }

  // ========== 用户相关接口 ==========

  /**
   * 微信登录
   */
  async wxLogin(code, userInfo) {
    return this.post('/api/user/wx-login', {
      code,
      userInfo
    }, { needAuth: false });
  }

  /**
   * 获取用户信息
   */
  async getUserInfo() {
    return this.get('/api/user/profile');
  }

  /**
   * 更新用户信息
   */
  async updateUserInfo(userInfo) {
    return this.put('/api/user/profile', userInfo);
  }

  // ========== 设备相关接口 ==========

  /**
   * 获取设备列表
   */
  async getDevices() {
    return this.get('/api/device');
  }

  /**
   * 获取设备详情
   */
  async getDeviceDetail(deviceId) {
    return this.get(`/api/device/${deviceId}`);
  }

  /**
   * 绑定设备
   */
  async bindDevice(deviceInfo) {
    return this.post('/api/device/register', deviceInfo);
  }

  /**
   * 更新设备配置
   */
  async updateDeviceConfig(deviceId, config) {
    return this.put(`/api/device/${deviceId}`, config);
  }

  /**
   * 删除设备
   */
  async deleteDevice(deviceId) {
    return this.delete(`/api/device/${deviceId}`);
  }

  /**
   * 设备配对
   */
  async pairDevice(deviceId, targetDeviceId) {
    return this.post(`/api/device/${deviceId}/pair`, {
      targetDeviceId
    });
  }

  // ========== 数据监控接口 ==========

  /**
   * 获取最新传感器数据
   */
  async getLatestSensorData(deviceId) {
    return this.get(`/api/data/latest`, { deviceId });
  }

  /**
   * 获取历史数据
   */
  async getHistoryData(deviceId, startTime, endTime, type) {
    return this.get(`/api/data/history`, {
      deviceId,
      startTime,
      endTime,
      type
    });
  }

  // ========== 宠物相关接口 ==========

  /**
   * 获取宠物列表
   */
  async getPets() {
    return this.get('/api/pet');
  }

  /**
   * 获取宠物详情
   */
  async getPetDetail(petId) {
    return this.get(`/api/pet/${petId}`);
  }

  /**
   * 创建宠物
   */
  async createPet(petInfo) {
    return this.post('/api/pet', petInfo);
  }

  /**
   * 更新宠物信息
   */
  async updatePet(petId, petInfo) {
    return this.put(`/api/pet/${petId}`, petInfo);
  }

  /**
   * 宠物交互
   */
  async petInteract(petId, action, params = {}) {
    return this.post(`/api/pet/${petId}/interact`, {
      action,
      ...params
    });
  }

  // ========== 任务相关接口 ==========

  /**
   * 获取任务列表
   */
  async getTasks(params = {}) {
    return this.get('/api/task', params);
  }

  /**
   * 获取任务详情
   */
  async getTaskDetail(taskId) {
    return this.get(`/api/task/${taskId}`);
  }

  /**
   * 创建任务
   */
  async createTask(taskInfo) {
    return this.post('/api/task', taskInfo);
  }

  /**
   * 更新任务
   */
  async updateTask(taskId, taskInfo) {
    return this.put(`/api/task/${taskId}`, taskInfo);
  }

  /**
   * 完成任务
   */
  async completeTask(taskId) {
    return this.post(`/api/task/${taskId}/complete`);
  }

  /**
   * 删除任务
   */
  async deleteTask(taskId) {
    return this.delete(`/api/task/${taskId}`);
  }

  /**
   * 获取今日任务
   */
  async getTodayTasks() {
    return this.get('/api/task/today');
  }

  /**
   * 获取任务统计
   */
  async getTaskStats() {
    return this.get('/api/task/stats/summary');
  }

  // ========== 场景相关接口 ==========

  /**
   * 获取场景列表
   */
  async getScenes() {
    return this.get('/api/scene');
  }

  /**
   * 切换场景
   */
  async switchScene(deviceId, sceneId) {
    return this.post(`/api/device/${deviceId}/scene`, {
      sceneId
    });
  }

  // ========== 主题相关接口 ==========

  /**
   * 获取主题列表
   */
  async getThemes(params = {}) {
    return this.get('/api/theme', params);
  }

  /**
   * 获取主题详情
   */
  async getThemeDetail(themeId) {
    return this.get(`/api/theme/${themeId}`);
  }

  /**
   * 下载主题
   */
  async downloadTheme(themeId) {
    return this.get(`/api/theme/${themeId}/download`);
  }

  /**
   * 应用主题
   */
  async applyTheme(deviceId, themeId) {
    return this.post(`/api/device/${deviceId}/theme`, {
      themeId
    });
  }

  // ========== 文件上传接口 ==========

  /**
   * 上传文件
   */
  uploadFile(filePath, name = 'file') {
    return new Promise((resolve, reject) => {
      wx.uploadFile({
        url: `${this.baseURL}/api/upload`,
        filePath,
        name,
        header: {
          'Authorization': `Bearer ${auth.getToken()}`
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            resolve(data);
          } catch (error) {
            reject(new Error('上传响应解析失败'));
          }
        },
        fail: (error) => {
          reject(new Error('文件上传失败'));
        }
      });
    });
  }

  // ========== 宠物相关接口 ==========

  /**
   * 获取用户宠物
   */
  async getPet() {
    return this.get('/api/pet');
  }

  /**
   * 创建宠物
   */
  async createPet(petData) {
    return this.post('/api/pet', petData);
  }

  /**
   * 更新宠物信息
   */
  async updatePet(updates) {
    return this.put('/api/pet', updates);
  }

  /**
   * 获取宠物状态
   */
  async getPetStatus() {
    return this.get('/api/pet/status');
  }

  /**
   * 宠物语音交互
   */
  async petVoiceInteraction(data) {
    return this.post('/api/pet/interact/voice', data);
  }

  /**
   * 宠物触摸交互
   */
  async petTouchInteraction(data) {
    return this.post('/api/pet/interact/touch', data);
  }

  /**
   * 宠物手势交互
   */
  async petGestureInteraction(data) {
    return this.post('/api/pet/interact/gesture', data);
  }

  /**
   * 和宠物玩耍
   */
  async petPlay(data) {
    return this.post('/api/pet/play', data);
  }

  /**
   * 喂食宠物
   */
  async petFeed(data) {
    return this.post('/api/pet/feed', data);
  }

  /**
   * 清洁宠物
   */
  async petClean() {
    return this.post('/api/pet/clean');
  }

  /**
   * 获取宠物交互历史
   */
  async getPetInteractions(params = {}) {
    return this.get('/api/pet/interactions', params);
  }

  /**
   * 获取宠物情感历史
   */
  async getPetEmotions(params = {}) {
    return this.get('/api/pet/emotions', params);
  }

  // ==================== 主题相关API ====================

  /**
   * 获取主题列表
   */
  async getThemes(params = {}) {
    return this.get('/api/theme', params);
  }

  /**
   * 获取单个主题详情
   */
  async getTheme(themeId) {
    return this.get(`/api/theme/${themeId}`);
  }

  /**
   * 创建新主题
   */
  async createTheme(themeData) {
    return this.post('/api/theme/create', themeData);
  }

  /**
   * 更新主题
   */
  async updateTheme(themeId, themeData) {
    return this.put(`/api/theme/${themeId}`, themeData);
  }

  /**
   * 删除主题
   */
  async deleteTheme(themeId) {
    return this.delete(`/api/theme/${themeId}`);
  }

  /**
   * 发布主题
   */
  async publishTheme(themeId) {
    return this.post(`/api/theme/${themeId}/publish`);
  }

  /**
   * 应用主题
   */
  async applyTheme(themeId) {
    return this.post(`/api/theme/${themeId}/apply`);
  }

  /**
   * 下载主题
   */
  async downloadTheme(themeId) {
    return this.post(`/api/theme/${themeId}/download`);
  }

  /**
   * 评价主题
   */
  async rateTheme(themeId, ratingData) {
    return this.post(`/api/theme/${themeId}/review`, ratingData);
  }

  /**
   * 举报主题
   */
  async reportTheme(themeId, reportData) {
    return this.post(`/api/theme/${themeId}/report`, reportData);
  }

  /**
   * 获取主题分类
   */
  async getThemeCategories() {
    return this.get('/api/theme/meta/categories');
  }

  /**
   * 搜索主题
   */
  async searchThemes(keyword, params = {}) {
    return this.get('/api/theme/search', { keyword, ...params });
  }

  /**
   * 获取用户的主题
   */
  async getUserThemes(params = {}) {
    return this.get('/api/theme/my', params);
  }

  /**
   * 获取热门主题
   */
  async getPopularThemes(params = {}) {
    return this.get('/api/theme/popular', params);
  }

  /**
   * 获取最新主题
   */
  async getLatestThemes(params = {}) {
    return this.get('/api/theme/latest', params);
  }
}

// 创建API实例
const api = new API();

module.exports = api;
