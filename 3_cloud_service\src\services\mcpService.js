/**
 * MCP (Model Context Protocol) 服务
 * <AUTHOR> Team
 * @version 1.0.0
 */

const axios = require('axios');
const logger = require('../utils/logger');
const Task = require('../models/Task');
const DeviceData = require('../models/DeviceData');

class MCPService {
  constructor() {
    this.tools = new Map();
    this.initializeTools();
  }

  /**
   * 初始化MCP工具
   */
  initializeTools() {
    // 时间相关工具
    this.tools.set('get_current_time', {
      name: 'get_current_time',
      description: '获取当前时间',
      inputSchema: {
        type: 'object',
        properties: {
          timezone: { type: 'string', description: '时区，默认为Asia/Shanghai' },
          format: { type: 'string', description: '时间格式，默认为ISO' }
        }
      },
      handler: this.getCurrentTime.bind(this)
    });

    this.tools.set('get_date_info', {
      name: 'get_date_info',
      description: '获取日期信息（星期、农历等）',
      inputSchema: {
        type: 'object',
        properties: {
          date: { type: 'string', description: '日期，默认为今天' }
        }
      },
      handler: this.getDateInfo.bind(this)
    });

    // 天气相关工具
    this.tools.set('get_weather', {
      name: 'get_weather',
      description: '获取天气信息',
      inputSchema: {
        type: 'object',
        properties: {
          location: { type: 'string', description: '位置' },
          type: { type: 'string', enum: ['current', 'forecast'], description: '天气类型' }
        },
        required: ['location']
      },
      handler: this.getWeather.bind(this)
    });

    this.tools.set('get_weather_alert', {
      name: 'get_weather_alert',
      description: '获取天气预警信息',
      inputSchema: {
        type: 'object',
        properties: {
          location: { type: 'string', description: '位置' }
        },
        required: ['location']
      },
      handler: this.getWeatherAlert.bind(this)
    });

    // 待办事项工具
    this.tools.set('create_task', {
      name: 'create_task',
      description: '创建待办事项',
      inputSchema: {
        type: 'object',
        properties: {
          title: { type: 'string', description: '任务标题' },
          description: { type: 'string', description: '任务描述' },
          dueDate: { type: 'string', description: '截止日期' },
          priority: { type: 'string', enum: ['low', 'medium', 'high'], description: '优先级' },
          userId: { type: 'string', description: '用户ID' }
        },
        required: ['title', 'userId']
      },
      handler: this.createTask.bind(this)
    });

    this.tools.set('get_tasks', {
      name: 'get_tasks',
      description: '获取待办事项列表',
      inputSchema: {
        type: 'object',
        properties: {
          userId: { type: 'string', description: '用户ID' },
          status: { type: 'string', enum: ['pending', 'completed', 'all'], description: '任务状态' },
          limit: { type: 'number', description: '返回数量限制' }
        },
        required: ['userId']
      },
      handler: this.getTasks.bind(this)
    });

    this.tools.set('update_task', {
      name: 'update_task',
      description: '更新待办事项',
      inputSchema: {
        type: 'object',
        properties: {
          taskId: { type: 'string', description: '任务ID' },
          updates: { type: 'object', description: '更新内容' },
          userId: { type: 'string', description: '用户ID' }
        },
        required: ['taskId', 'userId']
      },
      handler: this.updateTask.bind(this)
    });

    // 闹钟相关工具
    this.tools.set('set_alarm', {
      name: 'set_alarm',
      description: '设置闹钟',
      inputSchema: {
        type: 'object',
        properties: {
          deviceId: { type: 'string', description: '设备ID' },
          time: { type: 'string', description: '闹钟时间 (HH:MM)' },
          days: { type: 'array', items: { type: 'number' }, description: '重复日期 (0-6)' },
          name: { type: 'string', description: '闹钟名称' },
          sound: { type: 'string', description: '闹钟铃声' },
          enabled: { type: 'boolean', description: '是否启用' }
        },
        required: ['deviceId', 'time']
      },
      handler: this.setAlarm.bind(this)
    });

    this.tools.set('get_alarms', {
      name: 'get_alarms',
      description: '获取闹钟列表',
      inputSchema: {
        type: 'object',
        properties: {
          deviceId: { type: 'string', description: '设备ID' }
        },
        required: ['deviceId']
      },
      handler: this.getAlarms.bind(this)
    });

    // 氛围场景工具
    this.tools.set('switch_scene', {
      name: 'switch_scene',
      description: '切换氛围场景',
      inputSchema: {
        type: 'object',
        properties: {
          deviceId: { type: 'string', description: '设备ID' },
          sceneId: { type: 'string', description: '场景ID' },
          parameters: { type: 'object', description: '场景参数' }
        },
        required: ['deviceId', 'sceneId']
      },
      handler: this.switchScene.bind(this)
    });

    this.tools.set('get_scenes', {
      name: 'get_scenes',
      description: '获取可用场景列表',
      inputSchema: {
        type: 'object',
        properties: {
          deviceId: { type: 'string', description: '设备ID' }
        }
      },
      handler: this.getScenes.bind(this)
    });

    // 设备控制工具
    this.tools.set('control_device', {
      name: 'control_device',
      description: '控制设备',
      inputSchema: {
        type: 'object',
        properties: {
          deviceId: { type: 'string', description: '设备ID' },
          command: { type: 'string', description: '控制命令' },
          parameters: { type: 'object', description: '命令参数' }
        },
        required: ['deviceId', 'command']
      },
      handler: this.controlDevice.bind(this)
    });

    this.tools.set('get_device_status', {
      name: 'get_device_status',
      description: '获取设备状态',
      inputSchema: {
        type: 'object',
        properties: {
          deviceId: { type: 'string', description: '设备ID' }
        },
        required: ['deviceId']
      },
      handler: this.getDeviceStatus.bind(this)
    });

    // 信息查询工具
    this.tools.set('search_information', {
      name: 'search_information',
      description: '搜索信息',
      inputSchema: {
        type: 'object',
        properties: {
          query: { type: 'string', description: '搜索关键词' },
          type: { type: 'string', enum: ['general', 'news', 'knowledge'], description: '搜索类型' }
        },
        required: ['query']
      },
      handler: this.searchInformation.bind(this)
    });
  }

  /**
   * 获取当前时间
   */
  async getCurrentTime(params) {
    try {
      const { timezone = 'Asia/Shanghai', format = 'ISO' } = params;
      
      const now = new Date();
      let timeString;
      
      if (format === 'ISO') {
        timeString = now.toISOString();
      } else if (format === 'locale') {
        timeString = now.toLocaleString('zh-CN', { timeZone: timezone });
      } else {
        timeString = now.toString();
      }
      
      return {
        success: true,
        data: {
          time: timeString,
          timestamp: now.getTime(),
          timezone,
          weekday: now.toLocaleDateString('zh-CN', { weekday: 'long' })
        }
      };
    } catch (error) {
      logger.error('Get current time error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取日期信息
   */
  async getDateInfo(params) {
    try {
      const { date } = params;
      const targetDate = date ? new Date(date) : new Date();
      
      return {
        success: true,
        data: {
          date: targetDate.toISOString().split('T')[0],
          year: targetDate.getFullYear(),
          month: targetDate.getMonth() + 1,
          day: targetDate.getDate(),
          weekday: targetDate.toLocaleDateString('zh-CN', { weekday: 'long' }),
          isWeekend: targetDate.getDay() === 0 || targetDate.getDay() === 6,
          dayOfYear: Math.floor((targetDate - new Date(targetDate.getFullYear(), 0, 0)) / 86400000)
        }
      };
    } catch (error) {
      logger.error('Get date info error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取天气信息
   */
  async getWeather(params) {
    try {
      const { location, type = 'current' } = params;
      
      // TODO: 集成真实的天气API
      // 这里使用模拟数据
      const mockWeatherData = {
        current: {
          location,
          temperature: 25,
          humidity: 60,
          condition: '晴朗',
          windSpeed: 5,
          pressure: 1013,
          visibility: 10,
          uvIndex: 6
        },
        forecast: [
          { date: '今天', high: 28, low: 18, condition: '晴朗' },
          { date: '明天', high: 26, low: 16, condition: '多云' },
          { date: '后天', high: 24, low: 14, condition: '小雨' }
        ]
      };
      
      return {
        success: true,
        data: type === 'current' ? mockWeatherData.current : mockWeatherData.forecast
      };
    } catch (error) {
      logger.error('Get weather error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取天气预警
   */
  async getWeatherAlert(params) {
    try {
      const { location } = params;
      
      // TODO: 集成真实的天气预警API
      return {
        success: true,
        data: {
          location,
          alerts: [],
          message: '当前无天气预警信息'
        }
      };
    } catch (error) {
      logger.error('Get weather alert error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 创建任务
   */
  async createTask(params) {
    try {
      const { title, description, dueDate, priority = 'medium', userId } = params;
      
      const task = new Task({
        title,
        description,
        dueDate: dueDate ? new Date(dueDate) : null,
        priority,
        userId,
        status: 'pending',
        createdAt: new Date()
      });
      
      await task.save();
      
      return {
        success: true,
        data: {
          taskId: task._id,
          title: task.title,
          message: '任务创建成功'
        }
      };
    } catch (error) {
      logger.error('Create task error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取任务列表
   */
  async getTasks(params) {
    try {
      const { userId, status = 'all', limit = 10 } = params;
      
      const query = { userId };
      if (status !== 'all') {
        query.status = status;
      }
      
      const tasks = await Task.find(query)
        .sort({ createdAt: -1 })
        .limit(limit);
      
      return {
        success: true,
        data: {
          tasks: tasks.map(task => ({
            id: task._id,
            title: task.title,
            description: task.description,
            status: task.status,
            priority: task.priority,
            dueDate: task.dueDate,
            createdAt: task.createdAt
          })),
          total: tasks.length
        }
      };
    } catch (error) {
      logger.error('Get tasks error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 更新任务
   */
  async updateTask(params) {
    try {
      const { taskId, updates, userId } = params;
      
      const task = await Task.findOneAndUpdate(
        { _id: taskId, userId },
        { ...updates, updatedAt: new Date() },
        { new: true }
      );
      
      if (!task) {
        return { success: false, error: '任务不存在' };
      }
      
      return {
        success: true,
        data: {
          taskId: task._id,
          message: '任务更新成功'
        }
      };
    } catch (error) {
      logger.error('Update task error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 设置闹钟
   */
  async setAlarm(params) {
    try {
      const { deviceId, time, days = [], name = '闹钟', sound = 'default', enabled = true } = params;
      
      const device = await DeviceData.findOne({ deviceId });
      if (!device) {
        return { success: false, error: '设备不存在' };
      }
      
      const alarmId = `alarm_${Date.now()}`;
      const alarm = {
        id: alarmId,
        name,
        time,
        days,
        enabled,
        sound
      };
      
      device.config.alarms.push(alarm);
      await device.save();
      
      return {
        success: true,
        data: {
          alarmId,
          message: '闹钟设置成功'
        }
      };
    } catch (error) {
      logger.error('Set alarm error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取闹钟列表
   */
  async getAlarms(params) {
    try {
      const { deviceId } = params;
      
      const device = await DeviceData.findOne({ deviceId });
      if (!device) {
        return { success: false, error: '设备不存在' };
      }
      
      return {
        success: true,
        data: {
          alarms: device.config.alarms || []
        }
      };
    } catch (error) {
      logger.error('Get alarms error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 切换场景
   */
  async switchScene(params) {
    try {
      const { deviceId, sceneId, parameters = {} } = params;
      
      // TODO: 通过MQTT发送场景切换命令
      
      return {
        success: true,
        data: {
          deviceId,
          sceneId,
          message: '场景切换成功'
        }
      };
    } catch (error) {
      logger.error('Switch scene error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取场景列表
   */
  async getScenes(params) {
    try {
      const scenes = [
        { id: 'morning', name: '晨间唤醒', description: '温暖的晨光效果' },
        { id: 'work', name: '工作专注', description: '提高专注力的白光' },
        { id: 'rest', name: '休息放松', description: '舒缓的暖光效果' },
        { id: 'sleep', name: '助眠模式', description: '柔和的夜灯效果' },
        { id: 'party', name: '聚会模式', description: '动感的彩色灯效' },
        { id: 'reading', name: '阅读模式', description: '护眼的阅读灯光' }
      ];
      
      return {
        success: true,
        data: { scenes }
      };
    } catch (error) {
      logger.error('Get scenes error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 控制设备
   */
  async controlDevice(params) {
    try {
      const { deviceId, command, parameters = {} } = params;
      
      // TODO: 通过MQTT发送设备控制命令
      
      return {
        success: true,
        data: {
          deviceId,
          command,
          message: '设备控制命令已发送'
        }
      };
    } catch (error) {
      logger.error('Control device error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取设备状态
   */
  async getDeviceStatus(params) {
    try {
      const { deviceId } = params;
      
      const device = await DeviceData.findOne({ deviceId });
      if (!device) {
        return { success: false, error: '设备不存在' };
      }
      
      return {
        success: true,
        data: {
          deviceId,
          name: device.name,
          status: device.status,
          lastOnline: device.lastOnlineAt,
          config: device.config
        }
      };
    } catch (error) {
      logger.error('Get device status error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 搜索信息
   */
  async searchInformation(params) {
    try {
      const { query, type = 'general' } = params;
      
      // TODO: 集成真实的搜索API
      return {
        success: true,
        data: {
          query,
          type,
          results: [
            {
              title: `关于"${query}"的信息`,
              content: '这是一个示例搜索结果。',
              source: '示例来源'
            }
          ]
        }
      };
    } catch (error) {
      logger.error('Search information error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 执行MCP工具
   */
  async executeTool(toolName, params) {
    try {
      const tool = this.tools.get(toolName);
      if (!tool) {
        return { success: false, error: `未知工具: ${toolName}` };
      }
      
      logger.info(`执行MCP工具: ${toolName}`, params);
      const result = await tool.handler(params);
      
      return result;
    } catch (error) {
      logger.error(`MCP工具执行失败: ${toolName}`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取可用工具列表
   */
  getAvailableTools() {
    return Array.from(this.tools.values()).map(tool => ({
      name: tool.name,
      description: tool.description,
      inputSchema: tool.inputSchema
    }));
  }

  /**
   * 处理自然语言请求
   */
  async processNaturalLanguageRequest(request, context = {}) {
    try {
      // TODO: 集成NLP模型，解析自然语言请求并映射到相应的工具调用
      
      // 简单的关键词匹配示例
      const { text, userId, deviceId } = request;
      const lowerText = text.toLowerCase();
      
      if (lowerText.includes('时间') || lowerText.includes('几点')) {
        return await this.executeTool('get_current_time', {});
      }
      
      if (lowerText.includes('天气')) {
        const location = context.location || '北京';
        return await this.executeTool('get_weather', { location });
      }
      
      if (lowerText.includes('创建') && lowerText.includes('任务')) {
        // 提取任务标题
        const title = text.replace(/创建|任务|待办/g, '').trim();
        return await this.executeTool('create_task', { title, userId });
      }
      
      if (lowerText.includes('闹钟')) {
        if (deviceId) {
          return await this.executeTool('get_alarms', { deviceId });
        }
      }
      
      if (lowerText.includes('场景') || lowerText.includes('氛围')) {
        if (deviceId) {
          return await this.executeTool('get_scenes', { deviceId });
        }
      }
      
      return {
        success: false,
        error: '无法理解您的请求，请尝试更具体的描述'
      };
    } catch (error) {
      logger.error('Process natural language request error:', error);
      return { success: false, error: error.message };
    }
  }
}

module.exports = new MCPService();
