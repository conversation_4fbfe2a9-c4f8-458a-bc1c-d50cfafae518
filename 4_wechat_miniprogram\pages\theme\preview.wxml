<!--主题预览页面-->
<view class="preview-page {{isFullscreen ? 'fullscreen' : ''}}">
  <!-- 顶部控制栏 -->
  <view class="top-controls" wx:if="{{showControls && !isFullscreen}}">
    <view class="control-left">
      <view class="back-btn" bindtap="navigateBack">
        <van-icon name="arrow-left" size="20px" />
      </view>
    </view>
    
    <view class="control-center">
      <view class="theme-title">{{theme.name}}</view>
      <view class="theme-author">by {{theme.author}}</view>
    </view>
    
    <view class="control-right">
      <view class="control-btn" bindtap="onToggleFullscreen">
        <van-icon name="{{isFullscreen ? 'shrink' : 'enlarge'}}" size="18px" />
      </view>
      <view class="control-btn" bindtap="onShareAppMessage">
        <van-icon name="share" size="18px" />
      </view>
    </view>
  </view>

  <!-- 设备预览 -->
  <view class="device-preview" bindtap="onToggleControls">
    <view class="device-frame {{isFullscreen ? 'fullscreen-frame' : ''}}">
      <!-- 屏幕内容 -->
      <view class="screen-content">
        <!-- 背景 -->
        <view 
          class="preview-background"
          style="{{getBackgroundStyle(theme.config.background)}}"
        ></view>
        
        <!-- 表盘 -->
        <view 
          class="preview-watchface"
          style="{{getWatchfaceStyle(theme.config.watchface)}}"
        >
          <text class="time-text">{{mockData.time}}</text>
        </view>
        
        <!-- 组件渲染 -->
        <view 
          class="preview-widget widget-{{item.type}}"
          wx:for="{{theme.config.widgets}}"
          wx:key="id"
          style="{{getWidgetStyle(item)}}"
        >
          <!-- 时钟组件 -->
          <block wx:if="{{item.type === 'clock'}}">
            <view class="clock-display">
              <text class="clock-time">{{mockData.time}}</text>
              <text class="clock-date">{{mockData.date}}</text>
            </view>
          </block>
          
          <!-- 天气组件 -->
          <block wx:if="{{item.type === 'weather'}}">
            <view class="weather-display">
              <van-icon name="{{mockData.weather.icon}}" size="24px" />
              <view class="weather-info">
                <text class="temperature">{{mockData.weather.temperature}}°</text>
                <text class="condition">{{mockData.weather.condition}}</text>
              </view>
            </view>
          </block>
          
          <!-- 电量组件 -->
          <block wx:if="{{item.type === 'battery'}}">
            <view class="battery-display">
              <van-icon name="battery" size="20px" />
              <text class="battery-percent">{{mockData.battery}}%</text>
              <view class="battery-bar">
                <view 
                  class="battery-fill"
                  style="width: {{mockData.battery}}%"
                ></view>
              </view>
            </view>
          </block>
          
          <!-- 日期组件 -->
          <block wx:if="{{item.type === 'date'}}">
            <view class="date-display">
              <text class="date-text">{{mockData.date}}</text>
            </view>
          </block>
          
          <!-- 传感器组件 -->
          <block wx:if="{{item.type === 'sensor'}}">
            <view class="sensor-display">
              <van-icon name="chart" size="20px" />
              <view class="sensor-info">
                <text class="sensor-label">{{getSensorLabel(item.config.type)}}</text>
                <text class="sensor-value">{{mockData.sensors[item.config.type]}}</text>
              </view>
            </view>
          </block>
          
          <!-- 宠物组件 -->
          <block wx:if="{{item.type === 'pet'}}">
            <view class="pet-display">
              <view class="pet-avatar {{mockData.pet.emotion}}">
                <van-icon name="smile-o" size="32px" />
              </view>
              <view class="pet-info">
                <text class="pet-name">{{mockData.pet.name}}</text>
                <text class="pet-level">Lv.{{mockData.pet.level}}</text>
              </view>
            </view>
          </block>
        </view>
        
        <!-- 动画效果 -->
        <view class="animation-layer" wx:if="{{animationData}}">
          <!-- 这里可以添加各种动画效果 -->
        </view>
      </view>
    </view>
  </view>

  <!-- 底部控制栏 -->
  <view class="bottom-controls" wx:if="{{showControls && !isFullscreen}}">
    <!-- 主题信息 -->
    <view class="theme-info">
      <view class="info-row">
        <view class="info-item">
          <van-icon name="star" size="14px" />
          <text>{{theme.rating}}</text>
        </view>
        <view class="info-item">
          <van-icon name="eye" size="14px" />
          <text>{{theme.downloads}}</text>
        </view>
        <view class="info-item">
          <van-icon name="clock" size="14px" />
          <text>{{theme.updateTime}}</text>
        </view>
      </view>
      <view class="theme-description">{{theme.description}}</view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <view class="action-btn secondary" bindtap="onDownloadTheme">
        <van-icon name="down" size="16px" />
        <text>下载</text>
      </view>
      <view class="action-btn secondary" bindtap="onShowRating">
        <van-icon name="star-o" size="16px" />
        <text>评价</text>
      </view>
      <view class="action-btn primary" bindtap="onApplyTheme">
        <van-icon name="success" size="16px" />
        <text>应用主题</text>
      </view>
    </view>
  </view>

  <!-- 全屏模式控制 -->
  <view class="fullscreen-controls" wx:if="{{isFullscreen && showControls}}">
    <view class="fs-control-btn" bindtap="onToggleFullscreen">
      <van-icon name="shrink" size="20px" color="#fff" />
    </view>
    <view class="fs-control-btn" bindtap="onApplyTheme">
      <van-icon name="success" size="20px" color="#fff" />
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <van-loading type="spinner" size="32px" />
    <text>加载主题中...</text>
  </view>
</view>

<!-- 评价弹窗 -->
<van-popup 
  show="{{showRating}}" 
  position="bottom" 
  round
  bind:close="onHideRating"
>
  <view class="rating-popup">
    <view class="popup-header">
      <text>评价主题</text>
      <van-icon name="cross" size="18px" bindtap="onHideRating" />
    </view>
    
    <view class="rating-content">
      <!-- 星级评分 -->
      <view class="rating-stars">
        <view 
          class="star-item {{index < userRating ? 'active' : ''}}"
          wx:for="{{[1,2,3,4,5]}}"
          wx:key="*this"
          data-rating="{{item}}"
          bindtap="onRating"
        >
          <van-icon name="star" size="24px" />
        </view>
      </view>
      
      <!-- 评论输入 -->
      <view class="comment-input">
        <van-field
          value="{{ratingComment}}"
          placeholder="说说你的使用感受..."
          type="textarea"
          maxlength="200"
          show-word-limit
          bind:change="onCommentInput"
        />
      </view>
      
      <!-- 提交按钮 -->
      <view class="submit-btn" bindtap="onSubmitRating">
        <van-button type="primary" block>提交评价</van-button>
      </view>
    </view>
  </view>
</van-popup>
