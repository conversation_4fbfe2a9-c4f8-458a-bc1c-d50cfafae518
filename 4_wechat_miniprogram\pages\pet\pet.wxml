<!--虚拟宠物页面-->
<view class="pet-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 宠物主界面 -->
  <view wx:else class="pet-main">
    <!-- 宠物信息卡片 -->
    <view class="pet-info-card">
      <view class="pet-avatar">
        <view class="pet-sprite {{petStatus.emotion.primary}}" 
              style="background-color: {{pet.appearance.primaryColor}}">
          <text class="pet-face">{{getEmotionIcon(petStatus.emotion.primary)}}</text>
        </view>
        <view class="pet-level">Lv.{{petStatus.basic.level}}</view>
      </view>
      
      <view class="pet-details">
        <view class="pet-name">{{pet.name}}</view>
        <view class="pet-status">
          <text class="status-text">{{petStatus.emotion.description}}</text>
          <text class="age-text">{{petStatus.basic.age}}天大</text>
        </view>
        
        <!-- 属性条 -->
        <view class="attributes">
          <view class="attribute-item">
            <text class="attr-label">健康</text>
            <view class="attr-bar">
              <view class="attr-fill" 
                    style="width: {{petStatus.attributes.health}}%; background-color: {{getAttributeColor(petStatus.attributes.health)}}"></view>
            </view>
            <text class="attr-value">{{petStatus.attributes.health}}</text>
          </view>
          
          <view class="attribute-item">
            <text class="attr-label">快乐</text>
            <view class="attr-bar">
              <view class="attr-fill" 
                    style="width: {{petStatus.attributes.happiness}}%; background-color: {{getAttributeColor(petStatus.attributes.happiness)}}"></view>
            </view>
            <text class="attr-value">{{petStatus.attributes.happiness}}</text>
          </view>
          
          <view class="attribute-item">
            <text class="attr-label">饥饿</text>
            <view class="attr-bar">
              <view class="attr-fill" 
                    style="width: {{100 - petStatus.attributes.hunger}}%; background-color: {{getAttributeColor(100 - petStatus.attributes.hunger)}}"></view>
            </view>
            <text class="attr-value">{{100 - petStatus.attributes.hunger}}</text>
          </view>
        </view>
      </view>
      
      <!-- 自定义按钮 -->
      <view class="customize-btn" bindtap="showCustomizePanel">
        <text class="iconfont icon-edit"></text>
      </view>
    </view>

    <!-- 交互按钮区域 -->
    <view class="interaction-section">
      <view class="section-title">与{{pet.name}}互动</view>
      
      <view class="interaction-grid">
        <view wx:for="{{interactionButtons}}" wx:key="id" 
              class="interaction-btn {{interacting && currentInteraction === item.id ? 'active' : ''}}"
              style="background-color: {{item.color}}"
              data-interaction="{{item.id}}"
              bindtap="onInteractionTap">
          <view class="btn-icon">
            <text class="iconfont icon-{{item.icon}}"></text>
          </view>
          <text class="btn-text">{{item.name}}</text>
          
          <!-- 交互中的动画 -->
          <view wx:if="{{interacting && currentInteraction === item.id}}" class="interaction-loading">
            <view class="loading-dots">
              <view class="dot"></view>
              <view class="dot"></view>
              <view class="dot"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 语音录音界面 -->
    <view wx:if="{{recording}}" class="recording-overlay">
      <view class="recording-modal">
        <view class="recording-animation">
          <view class="wave wave1"></view>
          <view class="wave wave2"></view>
          <view class="wave wave3"></view>
          <view class="microphone">🎤</view>
        </view>
        <text class="recording-text">正在录音... {{recordingTime}}s</text>
        <view class="recording-controls">
          <button class="stop-btn" bindtap="stopRecording">停止录音</button>
        </view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="section-title">互动统计</view>
      
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-number">{{petStatus.interactions.total}}</text>
          <text class="stat-label">总互动</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{petStatus.interactions.today}}</text>
          <text class="stat-label">今日互动</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{petStatus.health}}</text>
          <text class="stat-label">综合健康</text>
        </view>
      </view>
      
      <view class="history-btn" bindtap="showInteractionHistory">
        <text>查看互动历史</text>
        <text class="iconfont icon-arrow-right"></text>
      </view>
    </view>
  </view>

  <!-- 自定义面板 -->
  <view wx:if="{{showCustomize}}" class="customize-overlay">
    <view class="customize-modal">
      <view class="modal-header">
        <text class="modal-title">自定义{{pet.name}}</text>
        <view class="close-btn" bindtap="hideCustomizePanel">
          <text class="iconfont icon-close"></text>
        </view>
      </view>
      
      <view class="customize-content">
        <!-- 宠物类型 -->
        <view class="customize-section">
          <text class="section-label">宠物类型</text>
          <view class="type-options">
            <view wx:for="{{appearanceOptions.types}}" wx:key="value"
                  class="type-option {{pet.type === item.value ? 'selected' : ''}}"
                  data-type="type" data-value="{{item.value}}"
                  bindtap="updatePetAppearance">
              <text class="type-icon">{{item.icon}}</text>
              <text class="type-name">{{item.name}}</text>
            </view>
          </view>
        </view>
        
        <!-- 颜色选择 -->
        <view class="customize-section">
          <text class="section-label">主色调</text>
          <view class="color-options">
            <view wx:for="{{appearanceOptions.colors}}" wx:key="value"
                  class="color-option {{pet.appearance.primaryColor === item.value ? 'selected' : ''}}"
                  style="background-color: {{item.value}}"
                  data-type="primaryColor" data-value="{{item.value}}"
                  bindtap="updatePetAppearance">
              <text class="color-name">{{item.name}}</text>
            </view>
          </view>
        </view>
        
        <!-- 宠物名称 -->
        <view class="customize-section">
          <text class="section-label">宠物名称</text>
          <input class="name-input" 
                 value="{{pet.name}}" 
                 placeholder="给宠物起个名字"
                 maxlength="10"
                 bindinput="onNameInput" />
        </view>
      </view>
    </view>
  </view>

  <!-- 交互历史面板 -->
  <view wx:if="{{showInteractionHistory}}" class="history-overlay">
    <view class="history-modal">
      <view class="modal-header">
        <text class="modal-title">互动历史</text>
        <view class="close-btn" bindtap="hideInteractionHistory">
          <text class="iconfont icon-close"></text>
        </view>
      </view>
      
      <view class="history-content">
        <view wx:for="{{interactionHistory.interactions}}" wx:key="index" class="history-item">
          <view class="history-icon">
            <text class="iconfont icon-{{item.type}}"></text>
          </view>
          <view class="history-details">
            <text class="history-type">{{item.type}}</text>
            <text class="history-content">{{item.content}}</text>
            <text class="history-time">{{item.timestamp}}</text>
          </view>
          <view class="history-emotion">
            <text class="emotion-icon">{{getEmotionIcon(item.petResponse)}}</text>
          </view>
        </view>
        
        <view wx:if="{{!interactionHistory.interactions.length}}" class="empty-history">
          <text class="empty-text">还没有互动记录</text>
          <text class="empty-tip">快去和{{pet.name}}互动吧！</text>
        </view>
      </view>
    </view>
  </view>
</view>
