/**
 * MCP (Model Context Protocol) 路由
 * <AUTHOR> Team
 * @version 1.0.0
 */

const express = require('express');
const router = express.Router();
const authMiddleware = require('../middleware/auth');
const mcpService = require('../services/mcpService');
const logger = require('../utils/logger');

/**
 * 获取可用工具列表
 * GET /api/mcp/tools
 */
router.get('/tools', authMiddleware, async (req, res) => {
  try {
    const tools = mcpService.getAvailableTools();
    
    res.json({
      success: true,
      data: {
        tools,
        count: tools.length
      }
    });
  } catch (error) {
    logger.error('Get MCP tools error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get MCP tools'
    });
  }
});

/**
 * 执行MCP工具
 * POST /api/mcp/tools/:toolName
 */
router.post('/tools/:toolName', authMiddleware, async (req, res) => {
  try {
    const { toolName } = req.params;
    const params = {
      ...req.body,
      userId: req.user.id
    };

    const result = await mcpService.executeTool(toolName, params);
    
    if (result.success) {
      res.json({
        success: true,
        tool: toolName,
        result: result.data
      });
    } else {
      res.status(400).json({
        success: false,
        tool: toolName,
        error: result.error
      });
    }
  } catch (error) {
    logger.error('Execute MCP tool error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to execute MCP tool'
    });
  }
});

/**
 * 自然语言请求处理
 * POST /api/mcp/chat
 */
router.post('/chat', authMiddleware, async (req, res) => {
  try {
    const { text, deviceId, context = {} } = req.body;

    if (!text) {
      return res.status(400).json({
        error: 'Missing text',
        message: 'Text is required for natural language processing'
      });
    }

    const request = {
      text,
      userId: req.user.id,
      deviceId
    };

    const result = await mcpService.processNaturalLanguageRequest(request, context);
    
    if (result.success) {
      res.json({
        success: true,
        request: text,
        response: result.data,
        timestamp: new Date()
      });
    } else {
      res.status(400).json({
        success: false,
        request: text,
        error: result.error,
        timestamp: new Date()
      });
    }
  } catch (error) {
    logger.error('Process natural language request error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to process natural language request'
    });
  }
});

/**
 * 时间相关工具
 */

/**
 * 获取当前时间
 * GET /api/mcp/time/current
 */
router.get('/time/current', authMiddleware, async (req, res) => {
  try {
    const { timezone, format } = req.query;
    
    const result = await mcpService.executeTool('get_current_time', {
      timezone,
      format
    });
    
    res.json(result);
  } catch (error) {
    logger.error('Get current time error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get current time'
    });
  }
});

/**
 * 获取日期信息
 * GET /api/mcp/time/date
 */
router.get('/time/date', authMiddleware, async (req, res) => {
  try {
    const { date } = req.query;
    
    const result = await mcpService.executeTool('get_date_info', { date });
    
    res.json(result);
  } catch (error) {
    logger.error('Get date info error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get date info'
    });
  }
});

/**
 * 天气相关工具
 */

/**
 * 获取天气信息
 * GET /api/mcp/weather
 */
router.get('/weather', authMiddleware, async (req, res) => {
  try {
    const { location, type = 'current' } = req.query;

    if (!location) {
      return res.status(400).json({
        error: 'Missing location',
        message: 'Location is required'
      });
    }
    
    const result = await mcpService.executeTool('get_weather', {
      location,
      type
    });
    
    res.json(result);
  } catch (error) {
    logger.error('Get weather error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get weather'
    });
  }
});

/**
 * 获取天气预警
 * GET /api/mcp/weather/alert
 */
router.get('/weather/alert', authMiddleware, async (req, res) => {
  try {
    const { location } = req.query;

    if (!location) {
      return res.status(400).json({
        error: 'Missing location',
        message: 'Location is required'
      });
    }
    
    const result = await mcpService.executeTool('get_weather_alert', { location });
    
    res.json(result);
  } catch (error) {
    logger.error('Get weather alert error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get weather alert'
    });
  }
});

/**
 * 任务管理工具
 */

/**
 * 创建任务
 * POST /api/mcp/tasks
 */
router.post('/tasks', authMiddleware, async (req, res) => {
  try {
    const params = {
      ...req.body,
      userId: req.user.id
    };
    
    const result = await mcpService.executeTool('create_task', params);
    
    res.json(result);
  } catch (error) {
    logger.error('Create task via MCP error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to create task'
    });
  }
});

/**
 * 获取任务列表
 * GET /api/mcp/tasks
 */
router.get('/tasks', authMiddleware, async (req, res) => {
  try {
    const { status, limit } = req.query;
    
    const result = await mcpService.executeTool('get_tasks', {
      userId: req.user.id,
      status,
      limit: limit ? parseInt(limit) : undefined
    });
    
    res.json(result);
  } catch (error) {
    logger.error('Get tasks via MCP error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get tasks'
    });
  }
});

/**
 * 更新任务
 * PUT /api/mcp/tasks/:taskId
 */
router.put('/tasks/:taskId', authMiddleware, async (req, res) => {
  try {
    const { taskId } = req.params;
    const updates = req.body;
    
    const result = await mcpService.executeTool('update_task', {
      taskId,
      updates,
      userId: req.user.id
    });
    
    res.json(result);
  } catch (error) {
    logger.error('Update task via MCP error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to update task'
    });
  }
});

/**
 * 设备控制工具
 */

/**
 * 设置闹钟
 * POST /api/mcp/device/:deviceId/alarm
 */
router.post('/device/:deviceId/alarm', authMiddleware, async (req, res) => {
  try {
    const { deviceId } = req.params;
    const params = {
      ...req.body,
      deviceId
    };
    
    const result = await mcpService.executeTool('set_alarm', params);
    
    res.json(result);
  } catch (error) {
    logger.error('Set alarm via MCP error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to set alarm'
    });
  }
});

/**
 * 获取闹钟列表
 * GET /api/mcp/device/:deviceId/alarms
 */
router.get('/device/:deviceId/alarms', authMiddleware, async (req, res) => {
  try {
    const { deviceId } = req.params;
    
    const result = await mcpService.executeTool('get_alarms', { deviceId });
    
    res.json(result);
  } catch (error) {
    logger.error('Get alarms via MCP error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get alarms'
    });
  }
});

/**
 * 切换场景
 * POST /api/mcp/device/:deviceId/scene
 */
router.post('/device/:deviceId/scene', authMiddleware, async (req, res) => {
  try {
    const { deviceId } = req.params;
    const { sceneId, parameters } = req.body;

    if (!sceneId) {
      return res.status(400).json({
        error: 'Missing sceneId',
        message: 'Scene ID is required'
      });
    }
    
    const result = await mcpService.executeTool('switch_scene', {
      deviceId,
      sceneId,
      parameters
    });
    
    res.json(result);
  } catch (error) {
    logger.error('Switch scene via MCP error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to switch scene'
    });
  }
});

/**
 * 获取场景列表
 * GET /api/mcp/device/:deviceId/scenes
 */
router.get('/device/:deviceId/scenes', authMiddleware, async (req, res) => {
  try {
    const { deviceId } = req.params;
    
    const result = await mcpService.executeTool('get_scenes', { deviceId });
    
    res.json(result);
  } catch (error) {
    logger.error('Get scenes via MCP error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get scenes'
    });
  }
});

/**
 * 控制设备
 * POST /api/mcp/device/:deviceId/control
 */
router.post('/device/:deviceId/control', authMiddleware, async (req, res) => {
  try {
    const { deviceId } = req.params;
    const { command, parameters } = req.body;

    if (!command) {
      return res.status(400).json({
        error: 'Missing command',
        message: 'Command is required'
      });
    }
    
    const result = await mcpService.executeTool('control_device', {
      deviceId,
      command,
      parameters
    });
    
    res.json(result);
  } catch (error) {
    logger.error('Control device via MCP error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to control device'
    });
  }
});

/**
 * 获取设备状态
 * GET /api/mcp/device/:deviceId/status
 */
router.get('/device/:deviceId/status', authMiddleware, async (req, res) => {
  try {
    const { deviceId } = req.params;
    
    const result = await mcpService.executeTool('get_device_status', { deviceId });
    
    res.json(result);
  } catch (error) {
    logger.error('Get device status via MCP error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get device status'
    });
  }
});

/**
 * 信息搜索
 * GET /api/mcp/search
 */
router.get('/search', authMiddleware, async (req, res) => {
  try {
    const { query, type } = req.query;

    if (!query) {
      return res.status(400).json({
        error: 'Missing query',
        message: 'Search query is required'
      });
    }
    
    const result = await mcpService.executeTool('search_information', {
      query,
      type
    });
    
    res.json(result);
  } catch (error) {
    logger.error('Search information via MCP error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to search information'
    });
  }
});

module.exports = router;
