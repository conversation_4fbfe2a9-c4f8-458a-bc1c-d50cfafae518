/**
 * 虚拟宠物系统实现
 * <AUTHOR> Team
 * @version 1.0.0
 */

#include "pet_system.h"
#include "display_manager.h"
#include "audio_manager.h"
#include "tts_manager.h"
#include "esp_log.h"
#include "esp_timer.h"
#include <string.h>
#include <math.h>

static const char* TAG = "PET_SYSTEM";

// 全局变量
static pet_config_t g_pet_config = {0};
static pet_system_config_t g_system_config = {0};
static TaskHandle_t g_pet_task_handle = NULL;
static TaskHandle_t g_render_task_handle = NULL;
static TaskHandle_t g_emotion_task_handle = NULL;
static QueueHandle_t g_pet_event_queue = NULL;
static bool g_pet_system_initialized = false;
static bool g_task_mode = false;
static uint32_t g_animation_frame = 0;
static uint32_t g_last_frame_time = 0;

// 宠物动画数据（简化版，实际应该从文件系统加载）
static const pet_animation_data_t g_pet_animations[PET_ANIM_MAX] = {
    [PET_ANIM_IDLE] = {.frame_count = 4, .frame_delay = 500, .loop = true},
    [PET_ANIM_WALK] = {.frame_count = 6, .frame_delay = 200, .loop = true},
    [PET_ANIM_RUN] = {.frame_count = 8, .frame_delay = 100, .loop = true},
    [PET_ANIM_JUMP] = {.frame_count = 12, .frame_delay = 80, .loop = false},
    [PET_ANIM_SLEEP] = {.frame_count = 2, .frame_delay = 1000, .loop = true},
    [PET_ANIM_EAT] = {.frame_count = 8, .frame_delay = 150, .loop = false},
    [PET_ANIM_PLAY] = {.frame_count = 10, .frame_delay = 120, .loop = true},
    [PET_ANIM_HAPPY] = {.frame_count = 6, .frame_delay = 200, .loop = false},
    [PET_ANIM_SAD] = {.frame_count = 4, .frame_delay = 400, .loop = false},
    [PET_ANIM_EXCITED] = {.frame_count = 8, .frame_delay = 100, .loop = false},
    [PET_ANIM_SCARED] = {.frame_count = 6, .frame_delay = 150, .loop = false},
    [PET_ANIM_COMFORT] = {.frame_count = 8, .frame_delay = 300, .loop = false},
    [PET_ANIM_SHRINK] = {.frame_count = 10, .frame_delay = 50, .loop = false},
    [PET_ANIM_GUIDE] = {.frame_count = 6, .frame_delay = 200, .loop = true}
};

// 情感到动画的映射
static const pet_animation_t g_emotion_animations[PET_EMOTION_MAX] = {
    [PET_EMOTION_HAPPY] = PET_ANIM_HAPPY,
    [PET_EMOTION_SAD] = PET_ANIM_SAD,
    [PET_EMOTION_EXCITED] = PET_ANIM_EXCITED,
    [PET_EMOTION_CALM] = PET_ANIM_IDLE,
    [PET_EMOTION_ANGRY] = PET_ANIM_SCARED,
    [PET_EMOTION_SCARED] = PET_ANIM_SCARED,
    [PET_EMOTION_CURIOUS] = PET_ANIM_WALK,
    [PET_EMOTION_SLEEPY] = PET_ANIM_SLEEP,
    [PET_EMOTION_PLAYFUL] = PET_ANIM_PLAY
};

// 情感到声音的映射
static const char* g_emotion_sounds[PET_EMOTION_MAX] = {
    [PET_EMOTION_HAPPY] = "happy_chirp.wav",
    [PET_EMOTION_SAD] = "sad_whimper.wav",
    [PET_EMOTION_EXCITED] = "excited_bark.wav",
    [PET_EMOTION_CALM] = "gentle_purr.wav",
    [PET_EMOTION_ANGRY] = "angry_hiss.wav",
    [PET_EMOTION_SCARED] = "scared_whine.wav",
    [PET_EMOTION_CURIOUS] = "curious_chirp.wav",
    [PET_EMOTION_SLEEPY] = "sleepy_yawn.wav",
    [PET_EMOTION_PLAYFUL] = "playful_bark.wav"
};

// 私有函数声明
static void pet_render_frame(void);
static void pet_update_emotion_auto(void);
static pet_animation_t pet_get_emotion_animation(pet_emotion_t emotion);
static void pet_play_emotion_sound(pet_emotion_t emotion);
static void pet_draw_sprite(int16_t x, int16_t y, uint8_t size, pet_animation_t anim, uint32_t frame);

/**
 * 初始化宠物系统
 */
esp_err_t pet_system_init(const pet_system_config_t* config) {
    if (g_pet_system_initialized) {
        ESP_LOGW(TAG, "Pet system already initialized");
        return ESP_OK;
    }

    if (!config) {
        ESP_LOGE(TAG, "Invalid config parameter");
        return ESP_ERR_INVALID_ARG;
    }

    // 复制配置
    memcpy(&g_system_config, config, sizeof(pet_system_config_t));

    // 初始化默认宠物配置
    strcpy(g_pet_config.name, "小TIMO");
    g_pet_config.type = PET_TYPE_CAT;
    g_pet_config.appearance.primary_color = 0xFF6B6B;
    g_pet_config.appearance.secondary_color = 0x4ECDC4;
    g_pet_config.appearance.eye_color = 0x333333;
    g_pet_config.attributes.health = 100;
    g_pet_config.attributes.happiness = 80;
    g_pet_config.attributes.hunger = 20;
    g_pet_config.attributes.fatigue = 10;
    g_pet_config.attributes.cleanliness = 90;
    g_pet_config.attributes.intelligence = 50;
    g_pet_config.emotion.primary = PET_EMOTION_HAPPY;
    g_pet_config.emotion.intensity = 70;
    g_pet_config.display.position.x = 120;
    g_pet_config.display.position.y = 120;
    g_pet_config.display.animation = PET_ANIM_IDLE;
    g_pet_config.display.size = 100;
    g_pet_config.display.visible = true;
    g_pet_config.display.alpha = 255;
    g_pet_config.state = PET_STATE_IDLE;
    g_pet_config.level = 1;
    g_pet_config.experience = 0;
    g_pet_config.enabled = true;

    // 创建事件队列
    g_pet_event_queue = xQueueCreate(10, sizeof(pet_event_t));
    if (!g_pet_event_queue) {
        ESP_LOGE(TAG, "Failed to create pet event queue");
        return ESP_ERR_NO_MEM;
    }

    // 创建宠物系统任务
    BaseType_t ret = xTaskCreate(
        pet_system_task,
        "pet_system",
        4096,
        NULL,
        5,
        &g_pet_task_handle
    );
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create pet system task");
        vQueueDelete(g_pet_event_queue);
        return ESP_ERR_NO_MEM;
    }

    // 创建渲染任务
    ret = xTaskCreate(
        pet_render_task,
        "pet_render",
        3072,
        NULL,
        6,
        &g_render_task_handle
    );
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create pet render task");
        vTaskDelete(g_pet_task_handle);
        vQueueDelete(g_pet_event_queue);
        return ESP_ERR_NO_MEM;
    }

    // 创建情感更新任务
    if (g_system_config.auto_emotion_update) {
        ret = xTaskCreate(
            pet_emotion_task,
            "pet_emotion",
            2048,
            NULL,
            4,
            &g_emotion_task_handle
        );
        if (ret != pdPASS) {
            ESP_LOGE(TAG, "Failed to create pet emotion task");
            vTaskDelete(g_pet_task_handle);
            vTaskDelete(g_render_task_handle);
            vQueueDelete(g_pet_event_queue);
            return ESP_ERR_NO_MEM;
        }
    }

    g_pet_system_initialized = true;
    ESP_LOGI(TAG, "Pet system initialized successfully");

    return ESP_OK;
}

/**
 * 反初始化宠物系统
 */
esp_err_t pet_system_deinit(void) {
    if (!g_pet_system_initialized) {
        return ESP_OK;
    }

    // 删除任务
    if (g_pet_task_handle) {
        vTaskDelete(g_pet_task_handle);
        g_pet_task_handle = NULL;
    }
    if (g_render_task_handle) {
        vTaskDelete(g_render_task_handle);
        g_render_task_handle = NULL;
    }
    if (g_emotion_task_handle) {
        vTaskDelete(g_emotion_task_handle);
        g_emotion_task_handle = NULL;
    }

    // 删除队列
    if (g_pet_event_queue) {
        vQueueDelete(g_pet_event_queue);
        g_pet_event_queue = NULL;
    }

    g_pet_system_initialized = false;
    ESP_LOGI(TAG, "Pet system deinitialized");

    return ESP_OK;
}

/**
 * 设置宠物配置
 */
esp_err_t pet_set_config(const pet_config_t* config) {
    if (!config) {
        return ESP_ERR_INVALID_ARG;
    }

    memcpy(&g_pet_config, config, sizeof(pet_config_t));
    ESP_LOGI(TAG, "Pet config updated");

    return ESP_OK;
}

/**
 * 获取宠物配置
 */
esp_err_t pet_get_config(pet_config_t* config) {
    if (!config) {
        return ESP_ERR_INVALID_ARG;
    }

    memcpy(config, &g_pet_config, sizeof(pet_config_t));
    return ESP_OK;
}

/**
 * 更新宠物情感
 */
esp_err_t pet_update_emotion(pet_emotion_t emotion, uint8_t intensity) {
    if (emotion >= PET_EMOTION_MAX) {
        return ESP_ERR_INVALID_ARG;
    }

    g_pet_config.emotion.primary = emotion;
    g_pet_config.emotion.intensity = intensity;
    g_pet_config.emotion.last_update = esp_timer_get_time() / 1000;

    // 设置对应的动画
    pet_animation_t anim = pet_get_emotion_animation(emotion);
    pet_set_animation(anim);

    // 播放对应的声音
    pet_play_emotion_sound(emotion);

    // 调用回调函数
    if (g_system_config.emotion_callback) {
        g_system_config.emotion_callback(emotion, intensity);
    }

    ESP_LOGI(TAG, "Pet emotion updated: %d, intensity: %d", emotion, intensity);

    return ESP_OK;
}

/**
 * 设置宠物动画
 */
esp_err_t pet_set_animation(pet_animation_t animation) {
    if (animation >= PET_ANIM_MAX) {
        return ESP_ERR_INVALID_ARG;
    }

    g_pet_config.display.animation = animation;
    g_animation_frame = 0;  // 重置动画帧

    // 调用回调函数
    if (g_system_config.animation_callback) {
        g_system_config.animation_callback(animation);
    }

    ESP_LOGD(TAG, "Pet animation set to: %d", animation);

    return ESP_OK;
}

/**
 * 设置宠物位置
 */
esp_err_t pet_set_position(int16_t x, int16_t y) {
    g_pet_config.display.position.x = x;
    g_pet_config.display.position.y = y;

    ESP_LOGD(TAG, "Pet position set to: (%d, %d)", x, y);

    return ESP_OK;
}

/**
 * 设置宠物大小
 */
esp_err_t pet_set_size(uint8_t size) {
    if (size < 20 || size > 100) {
        return ESP_ERR_INVALID_ARG;
    }

    g_pet_config.display.size = size;
    ESP_LOGD(TAG, "Pet size set to: %d", size);

    return ESP_OK;
}

/**
 * 设置宠物可见性
 */
esp_err_t pet_set_visibility(bool visible) {
    g_pet_config.display.visible = visible;
    ESP_LOGD(TAG, "Pet visibility set to: %s", visible ? "true" : "false");

    return ESP_OK;
}

/**
 * 处理宠物交互
 */
esp_err_t pet_process_interaction(const pet_event_t* event) {
    if (!event || !g_pet_event_queue) {
        return ESP_ERR_INVALID_ARG;
    }

    // 将事件发送到队列
    BaseType_t ret = xQueueSend(g_pet_event_queue, event, pdMS_TO_TICKS(100));
    if (ret != pdPASS) {
        ESP_LOGW(TAG, "Failed to send pet event to queue");
        return ESP_ERR_TIMEOUT;
    }

    return ESP_OK;
}

/**
 * 宠物表情表达
 */
esp_err_t pet_express_emotion(pet_emotion_t emotion, uint16_t duration_ms) {
    esp_err_t ret = pet_update_emotion(emotion, 90);
    if (ret != ESP_OK) {
        return ret;
    }

    // 如果指定了持续时间，设置定时器恢复原来的情感
    if (duration_ms > 0) {
        // TODO: 实现定时器恢复情感
    }

    return ESP_OK;
}

/**
 * 宠物任务模式（缩小并引导）
 */
esp_err_t pet_enter_task_mode(bool enable) {
    g_task_mode = enable;

    if (enable) {
        // 缩小宠物并移动到角落
        pet_set_size(30);
        pet_set_position(200, 50);
        pet_set_animation(PET_ANIM_GUIDE);
        g_pet_config.state = PET_STATE_HELPING;
    } else {
        // 恢复正常大小和位置
        pet_set_size(100);
        pet_set_position(120, 120);
        pet_set_animation(PET_ANIM_IDLE);
        g_pet_config.state = PET_STATE_IDLE;
    }

    ESP_LOGI(TAG, "Pet task mode: %s", enable ? "enabled" : "disabled");

    return ESP_OK;
}

/**
 * 宠物睡眠模式
 */
esp_err_t pet_enter_sleep_mode(bool enable) {
    if (enable) {
        pet_set_animation(PET_ANIM_SLEEP);
        pet_update_emotion(PET_EMOTION_SLEEPY, 80);
        g_pet_config.state = PET_STATE_SLEEPING;
    } else {
        pet_set_animation(PET_ANIM_IDLE);
        pet_update_emotion(PET_EMOTION_CALM, 60);
        g_pet_config.state = PET_STATE_IDLE;
    }

    ESP_LOGI(TAG, "Pet sleep mode: %s", enable ? "enabled" : "disabled");

    return ESP_OK;
}

/**
 * 宠物播放声音
 */
esp_err_t pet_play_sound(const char* sound_name) {
    if (!sound_name) {
        return ESP_ERR_INVALID_ARG;
    }

    // 调用音频管理器播放声音
    return audio_manager_play_file(sound_name);
}

/**
 * 宠物说话（TTS）
 */
esp_err_t pet_speak(const char* text) {
    if (!text) {
        return ESP_ERR_INVALID_ARG;
    }

    // 调用TTS管理器
    return tts_manager_speak(text);
}

/**
 * 获取宠物动画数据
 */
const pet_animation_data_t* pet_get_animation_data(pet_animation_t animation) {
    if (animation >= PET_ANIM_MAX) {
        return NULL;
    }

    return &g_pet_animations[animation];
}

// 私有函数实现

/**
 * 获取情感对应的动画
 */
static pet_animation_t pet_get_emotion_animation(pet_emotion_t emotion) {
    if (emotion >= PET_EMOTION_MAX) {
        return PET_ANIM_IDLE;
    }

    return g_emotion_animations[emotion];
}

/**
 * 播放情感对应的声音
 */
static void pet_play_emotion_sound(pet_emotion_t emotion) {
    if (emotion >= PET_EMOTION_MAX) {
        return;
    }

    const char* sound_file = g_emotion_sounds[emotion];
    if (sound_file) {
        pet_play_sound(sound_file);
    }
}

/**
 * 渲染宠物帧
 */
static void pet_render_frame(void) {
    if (!g_pet_config.enabled || !g_pet_config.display.visible) {
        return;
    }

    // 获取当前动画数据
    const pet_animation_data_t* anim_data = pet_get_animation_data(g_pet_config.display.animation);
    if (!anim_data) {
        return;
    }

    // 检查是否需要更新动画帧
    uint32_t current_time = esp_timer_get_time() / 1000;
    if (current_time - g_last_frame_time >= anim_data->frame_delay) {
        g_animation_frame++;
        
        if (g_animation_frame >= anim_data->frame_count) {
            if (anim_data->loop) {
                g_animation_frame = 0;
            } else {
                g_animation_frame = anim_data->frame_count - 1;
                // 非循环动画结束后回到idle
                if (g_pet_config.display.animation != PET_ANIM_IDLE) {
                    pet_set_animation(PET_ANIM_IDLE);
                }
            }
        }
        
        g_last_frame_time = current_time;
    }

    // 绘制宠物精灵
    pet_draw_sprite(
        g_pet_config.display.position.x,
        g_pet_config.display.position.y,
        g_pet_config.display.size,
        g_pet_config.display.animation,
        g_animation_frame
    );
}

/**
 * 绘制宠物精灵
 */
static void pet_draw_sprite(int16_t x, int16_t y, uint8_t size, pet_animation_t anim, uint32_t frame) {
    // TODO: 实现实际的精灵绘制
    // 这里应该调用显示管理器的绘制函数
    // display_manager_draw_sprite(x, y, size, anim, frame, &g_pet_config.appearance);
    
    ESP_LOGD(TAG, "Drawing pet sprite at (%d, %d), size: %d, anim: %d, frame: %d", 
             x, y, size, anim, frame);
}

/**
 * 自动更新宠物情感
 */
static void pet_update_emotion_auto(void) {
    // 基于属性自动调整情感
    uint8_t happiness = g_pet_config.attributes.happiness;
    uint8_t health = g_pet_config.attributes.health;
    uint8_t hunger = g_pet_config.attributes.hunger;
    uint8_t fatigue = g_pet_config.attributes.fatigue;

    pet_emotion_t new_emotion = g_pet_config.emotion.primary;
    uint8_t new_intensity = g_pet_config.emotion.intensity;

    // 根据属性调整情感
    if (health < 30) {
        new_emotion = PET_EMOTION_SAD;
        new_intensity = 80;
    } else if (hunger > 80) {
        new_emotion = PET_EMOTION_SAD;
        new_intensity = 70;
    } else if (fatigue > 90) {
        new_emotion = PET_EMOTION_SLEEPY;
        new_intensity = 90;
    } else if (happiness > 80 && health > 80) {
        new_emotion = PET_EMOTION_HAPPY;
        new_intensity = happiness;
    } else if (happiness < 30) {
        new_emotion = PET_EMOTION_SAD;
        new_intensity = 60;
    }

    // 只有情感发生变化时才更新
    if (new_emotion != g_pet_config.emotion.primary || 
        abs(new_intensity - g_pet_config.emotion.intensity) > 10) {
        pet_update_emotion(new_emotion, new_intensity);
    }
}

/**
 * 宠物系统任务
 */
void pet_system_task(void* pvParameters) {
    pet_event_t event;
    
    ESP_LOGI(TAG, "Pet system task started");

    while (1) {
        // 处理事件队列
        if (xQueueReceive(g_pet_event_queue, &event, pdMS_TO_TICKS(100)) == pdPASS) {
            // 处理交互事件
            ESP_LOGI(TAG, "Processing pet interaction: type=%d, content=%s", 
                     event.type, event.content);

            // 根据交互类型更新宠物状态
            switch (event.type) {
                case PET_INTERACT_VOICE:
                    pet_express_emotion(PET_EMOTION_CURIOUS, 3000);
                    break;
                case PET_INTERACT_TOUCH:
                    pet_express_emotion(PET_EMOTION_HAPPY, 2000);
                    break;
                case PET_INTERACT_PLAY:
                    pet_express_emotion(PET_EMOTION_PLAYFUL, 5000);
                    break;
                case PET_INTERACT_FEED:
                    pet_express_emotion(PET_EMOTION_HAPPY, 3000);
                    g_pet_config.attributes.hunger = 
                        (g_pet_config.attributes.hunger > 30) ? 
                        g_pet_config.attributes.hunger - 30 : 0;
                    break;
                default:
                    break;
            }

            // 调用交互回调
            if (g_system_config.interaction_callback) {
                g_system_config.interaction_callback(event.type, event.content);
            }
        }

        // 定期更新
        vTaskDelay(pdMS_TO_TICKS(g_system_config.update_interval_ms));
    }
}

/**
 * 宠物渲染任务
 */
void pet_render_task(void* pvParameters) {
    ESP_LOGI(TAG, "Pet render task started");

    const uint16_t render_interval = 1000 / g_system_config.animation_fps;

    while (1) {
        pet_render_frame();
        vTaskDelay(pdMS_TO_TICKS(render_interval));
    }
}

/**
 * 宠物情感更新任务
 */
void pet_emotion_task(void* pvParameters) {
    ESP_LOGI(TAG, "Pet emotion task started");

    while (1) {
        pet_update_emotion_auto();
        vTaskDelay(pdMS_TO_TICKS(30000)); // 每30秒检查一次
    }
}
