/**
 * 登录页面
 */

const app = getApp();
const auth = require('../../utils/auth');

Page({
  data: {
    // 页面状态
    loading: false,
    
    // 用户信息
    userInfo: null,
    hasUserInfo: false,
    
    // 登录状态
    loginStep: 'welcome', // welcome, getUserInfo, login, success
    
    // 应用信息
    appInfo: {
      name: 'TIMO智能闹钟',
      version: '1.0.0',
      description: '让生活更智能，让时间更有趣'
    }
  },

  /**
   * 页面加载
   */
  onLoad() {
    console.log('登录页面加载');
    
    // 检查是否已登录
    if (auth.isLoggedIn()) {
      wx.reLaunch({
        url: '/pages/index/index'
      });
      return;
    }
    
    // 尝试获取用户信息
    this.checkUserInfo();
  },

  /**
   * 检查用户信息
   */
  async checkUserInfo() {
    try {
      const userInfo = await auth.getWxUserInfo();
      if (userInfo) {
        this.setData({
          userInfo,
          hasUserInfo: true,
          loginStep: 'login'
        });
      }
    } catch (error) {
      console.log('未获取到用户信息，需要用户授权');
    }
  },

  /**
   * 获取用户信息按钮点击
   */
  async onGetUserInfo(e) {
    try {
      const userInfo = await auth.getUserInfoByButton(e);
      
      this.setData({
        userInfo,
        hasUserInfo: true,
        loginStep: 'login'
      });
      
      console.log('获取用户信息成功:', userInfo);
    } catch (error) {
      console.error('获取用户信息失败:', error);
      wx.showToast({
        title: '需要授权才能使用',
        icon: 'none'
      });
    }
  },

  /**
   * 微信登录
   */
  async onWxLogin() {
    if (this.data.loading) {
      return;
    }

    this.setData({ loading: true });

    try {
      // 显示加载提示
      wx.showLoading({
        title: '登录中...',
        mask: true
      });

      // 执行登录
      const result = await auth.login(this.data.userInfo);
      
      console.log('登录成功:', result);
      
      // 更新应用全局数据
      app.globalData.token = result.token;
      app.globalData.userInfo = result.user;
      
      // 显示成功状态
      this.setData({ 
        loginStep: 'success'
      });
      
      // 延迟跳转到首页
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/index/index'
        });
      }, 1500);
      
    } catch (error) {
      console.error('登录失败:', error);
      
      wx.showModal({
        title: '登录失败',
        content: error.message || '网络异常，请稍后重试',
        showCancel: false
      });
      
    } finally {
      this.setData({ loading: false });
      wx.hideLoading();
    }
  },

  /**
   * 跳过登录（游客模式）
   */
  onSkipLogin() {
    wx.showModal({
      title: '提示',
      content: '游客模式下功能受限，建议登录获得完整体验',
      confirmText: '继续',
      cancelText: '登录',
      success: (res) => {
        if (res.confirm) {
          // 设置游客模式标识
          wx.setStorageSync('guestMode', true);
          
          wx.reLaunch({
            url: '/pages/index/index'
          });
        }
      }
    });
  },

  /**
   * 查看隐私政策
   */
  onPrivacyTap() {
    wx.navigateTo({
      url: '/pages/webview/webview?url=https://timo.com/privacy'
    });
  },

  /**
   * 查看用户协议
   */
  onTermsTap() {
    wx.navigateTo({
      url: '/pages/webview/webview?url=https://timo.com/terms'
    });
  },

  /**
   * 联系客服
   */
  onContactTap() {
    wx.makePhoneCall({
      phoneNumber: '************'
    });
  },

  /**
   * 关于我们
   */
  onAboutTap() {
    wx.navigateTo({
      url: '/pages/about/about'
    });
  }
});
