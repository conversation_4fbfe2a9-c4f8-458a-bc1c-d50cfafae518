/**
 * 设备详情页面
 * <AUTHOR> Team
 * @version 1.0.0
 */

const app = getApp();
const api = require('../../utils/api');

Page({
  data: {
    // 设备信息
    device: null,
    deviceId: '',
    
    // 设备状态
    status: 'offline',
    lastOnline: '',
    uptime: 0,
    
    // 实时数据
    realtimeData: [],
    
    // 统计信息
    stats: {
      uptime: 0,
      errorCount: 0,
      restartCount: 0,
      dataTransfer: { sent: 0, received: 0 }
    },
    
    // 配置信息
    config: {
      display: { brightness: 80, theme: 'default' },
      audio: { volume: 50, mute: false },
      network: { wifi: { connected: false } }
    },
    
    // 页面状态
    loading: true,
    refreshing: false,
    
    // 控制面板
    controlPanelVisible: false,
    
    // 操作按钮
    actions: [
      { id: 'restart', name: '重启设备', icon: 'restart', color: '#FF6B6B' },
      { id: 'reset', name: '恢复出厂', icon: 'reset', color: '#FFA726' },
      { id: 'update', name: '固件更新', icon: 'update', color: '#42A5F5' },
      { id: 'config', name: '设备配置', icon: 'config', color: '#66BB6A' }
    ]
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    console.log('设备详情页面加载', options);
    
    const { deviceId } = options;
    if (!deviceId) {
      app.showError('设备ID不能为空');
      wx.navigateBack();
      return;
    }

    this.setData({ deviceId });
    this.initPage();
  },

  /**
   * 页面显示
   */
  onShow() {
    console.log('设备详情页面显示');
    this.refreshData();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshData(true);
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 检查登录状态
      if (!app.globalData.token) {
        wx.reLaunch({
          url: '/pages/login/login'
        });
        return;
      }

      // 加载设备详情
      await this.loadDeviceDetail();
    } catch (error) {
      console.error('初始化设备详情页面失败:', error);
      app.showError('页面加载失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 刷新数据
   */
  async refreshData(isPullRefresh = false) {
    if (isPullRefresh) {
      this.setData({ refreshing: true });
    }

    try {
      await Promise.all([
        this.loadDeviceDetail(),
        this.loadRealtimeData(),
        this.loadDeviceStats()
      ]);
    } catch (error) {
      console.error('刷新数据失败:', error);
      app.showError('刷新失败');
    } finally {
      if (isPullRefresh) {
        this.setData({ refreshing: false });
        wx.stopPullDownRefresh();
      }
    }
  },

  /**
   * 加载设备详情
   */
  async loadDeviceDetail() {
    try {
      const device = await api.getDeviceDetail(this.data.deviceId);
      
      this.setData({
        device,
        status: device.status,
        lastOnline: this.formatTime(device.lastOnlineAt),
        config: device.config || this.data.config
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: device.name || '设备详情'
      });

      console.log('设备详情:', device);
    } catch (error) {
      console.error('加载设备详情失败:', error);
      throw error;
    }
  },

  /**
   * 加载实时数据
   */
  async loadRealtimeData() {
    try {
      const data = await api.getRealtimeData(this.data.deviceId);
      
      this.setData({ realtimeData: data });
      console.log('实时数据:', data);
    } catch (error) {
      console.error('加载实时数据失败:', error);
      // 不抛出错误，使用默认值
    }
  },

  /**
   * 加载设备统计
   */
  async loadDeviceStats() {
    try {
      const stats = await api.getDeviceStats(this.data.deviceId);
      
      this.setData({
        stats: stats.device || this.data.stats,
        uptime: this.formatUptime(stats.device?.uptime || 0)
      });

      console.log('设备统计:', stats);
    } catch (error) {
      console.error('加载设备统计失败:', error);
      // 不抛出错误，使用默认值
    }
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    if (!timestamp) return '--';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return '刚刚';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
    
    return date.toLocaleDateString();
  },

  /**
   * 格式化运行时间
   */
  formatUptime(seconds) {
    if (!seconds) return '0秒';
    
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) return `${days}天${hours}小时`;
    if (hours > 0) return `${hours}小时${minutes}分钟`;
    return `${minutes}分钟`;
  },

  /**
   * 获取状态颜色
   */
  getStatusColor(status) {
    const colors = {
      online: '#4CAF50',
      offline: '#9E9E9E',
      sleeping: '#FF9800',
      error: '#F44336'
    };
    return colors[status] || '#9E9E9E';
  },

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const texts = {
      online: '在线',
      offline: '离线',
      sleeping: '睡眠',
      error: '错误'
    };
    return texts[status] || '未知';
  },

  /**
   * 操作按钮点击
   */
  async onActionTap(e) {
    const { action } = e.currentTarget.dataset;
    
    switch (action) {
      case 'restart':
        await this.restartDevice();
        break;
      case 'reset':
        await this.resetDevice();
        break;
      case 'update':
        await this.updateFirmware();
        break;
      case 'config':
        this.openConfig();
        break;
      default:
        console.log('未知操作:', action);
    }
  },

  /**
   * 重启设备
   */
  async restartDevice() {
    const result = await new Promise(resolve => {
      wx.showModal({
        title: '确认重启',
        content: '确定要重启设备吗？设备将暂时离线。',
        success: resolve
      });
    });

    if (!result.confirm) return;

    try {
      app.showLoading('重启设备中...');
      
      await api.sendDeviceCommand(this.data.deviceId, 'restart');
      
      app.showSuccess('重启命令已发送');
      
      // 延迟刷新状态
      setTimeout(() => {
        this.refreshData();
      }, 3000);
    } catch (error) {
      console.error('重启设备失败:', error);
      app.showError('重启失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 恢复出厂设置
   */
  async resetDevice() {
    const result = await new Promise(resolve => {
      wx.showModal({
        title: '确认恢复出厂设置',
        content: '此操作将清除所有设置和数据，确定继续吗？',
        confirmColor: '#FF6B6B',
        success: resolve
      });
    });

    if (!result.confirm) return;

    try {
      app.showLoading('恢复出厂设置中...');
      
      await api.sendDeviceCommand(this.data.deviceId, 'factory_reset');
      
      app.showSuccess('恢复出厂设置成功');
      
      // 刷新数据
      setTimeout(() => {
        this.refreshData();
      }, 2000);
    } catch (error) {
      console.error('恢复出厂设置失败:', error);
      app.showError('操作失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 固件更新
   */
  async updateFirmware() {
    try {
      app.showLoading('检查更新中...');
      
      const updateInfo = await api.checkFirmwareUpdate(this.data.deviceId);
      
      if (!updateInfo.hasUpdate) {
        app.showSuccess('已是最新版本');
        return;
      }

      const result = await new Promise(resolve => {
        wx.showModal({
          title: '发现新版本',
          content: `发现新版本 ${updateInfo.version}，是否立即更新？`,
          success: resolve
        });
      });

      if (result.confirm) {
        await api.startFirmwareUpdate(this.data.deviceId);
        app.showSuccess('固件更新已开始');
      }
    } catch (error) {
      console.error('固件更新失败:', error);
      app.showError('更新失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 打开配置页面
   */
  openConfig() {
    wx.navigateTo({
      url: `/pages/config/config?deviceId=${this.data.deviceId}`
    });
  },

  /**
   * 显示控制面板
   */
  showControlPanel() {
    this.setData({ controlPanelVisible: true });
  },

  /**
   * 隐藏控制面板
   */
  hideControlPanel() {
    this.setData({ controlPanelVisible: false });
  },

  /**
   * 传感器数据点击
   */
  onSensorDataTap(e) {
    const { sensor } = e.currentTarget.dataset;
    
    wx.navigateTo({
      url: `/pages/monitor/history?deviceId=${this.data.deviceId}&sensorType=${sensor._id}`
    });
  },

  /**
   * 查看历史数据
   */
  onViewHistory() {
    wx.navigateTo({
      url: `/pages/monitor/history?deviceId=${this.data.deviceId}`
    });
  },

  /**
   * 设备重命名
   */
  async onRenameDevice() {
    const result = await new Promise(resolve => {
      wx.showModal({
        title: '设备重命名',
        editable: true,
        placeholderText: this.data.device.name,
        success: resolve
      });
    });

    if (!result.confirm || !result.content) return;

    try {
      app.showLoading('重命名中...');
      
      await api.updateDeviceConfig(this.data.deviceId, 'name', result.content);
      
      // 更新本地数据
      this.setData({
        'device.name': result.content
      });

      // 更新页面标题
      wx.setNavigationBarTitle({
        title: result.content
      });

      app.showSuccess('重命名成功');
    } catch (error) {
      console.error('设备重命名失败:', error);
      app.showError('重命名失败');
    } finally {
      app.hideLoading();
    }
  }
});
