# TIMO项目部署指南

## 项目概述

TIMO智能闹钟项目包含四个主要组件：
1. **主体固件** (1_main_device_firmware) - ESP32-S3主控设备
2. **底座固件** (2_base_station_firmware) - ESP32-C2底座设备  
3. **云端服务** (3_cloud_service) - Node.js后端服务
4. **微信小程序** (4_wechat_miniprogram) - 移动端控制应用

## 部署架构

```
┌─────────────────┐    ┌─────────────────┐
│   主体设备      │    │   底座设备      │
│  (ESP32-S3)     │◄──►│  (ESP32-C2)     │
│                 │    │                 │
│ • 语音交互      │    │ • 氛围灯光      │
│ • 环境监测      │    │ • 蓝牙通信      │
│ • 虚拟宠物      │    │ • 场景控制      │
│ • 任务管理      │    │                 │
└─────────┬───────┘    └─────────────────┘
          │
          │ WiFi/蓝牙
          │
┌─────────▼───────┐    ┌─────────────────┐
│   云端服务      │◄──►│   微信小程序    │
│  (Node.js)      │    │                 │
│                 │    │ • 设备管理      │
│ • 设备管理      │    │ • 状态监控      │
│ • 数据存储      │    │ • 宠物管理      │
│ • 语音服务      │    │ • 任务管理      │
│ • 推送通知      │    │ • 主题商店      │
└─────────────────┘    └─────────────────┘
```

## 环境要求

### 硬件要求
- **主体设备**: ESP32-S3开发板，2.8寸圆形LCD，触摸屏，音频模块，传感器模块
- **底座设备**: ESP32-C2开发板，WS2812 LED灯带，蓝牙模块
- **服务器**: 2核CPU，4GB内存，20GB存储空间

### 软件要求
- **固件开发**: ESP-IDF v5.0+, Python 3.8+
- **云端服务**: Node.js 16+, MongoDB 6.0+, Redis 7.0+
- **小程序开发**: 微信开发者工具，微信小程序账号

## 部署步骤

### 1. 固件编译和烧录

#### 1.1 环境准备
```bash
# 安装ESP-IDF
git clone --recursive https://github.com/espressif/esp-idf.git
cd esp-idf
./install.sh esp32,esp32s3,esp32c2
source export.sh
```

#### 1.2 编译主体固件
```bash
cd 1_main_device_firmware

# 设置目标芯片
idf.py set-target esp32s3

# 配置项目
idf.py menuconfig

# 编译固件
idf.py build

# 烧录固件
idf.py flash monitor
```

#### 1.3 编译底座固件
```bash
cd 2_base_station_firmware

# 设置目标芯片
idf.py set-target esp32c2

# 配置项目
idf.py menuconfig

# 编译固件
idf.py build

# 烧录固件
idf.py flash monitor
```

### 2. 云端服务部署

#### 2.1 环境准备
```bash
cd 3_cloud_service

# 安装依赖
npm install

# 复制环境配置
cp .env.example .env

# 编辑配置文件
vim .env
```

#### 2.2 数据库配置
```bash
# 启动MongoDB
sudo systemctl start mongod

# 启动Redis
sudo systemctl start redis

# 创建数据库用户
mongo
> use timo
> db.createUser({
    user: "timo",
    pwd: "password",
    roles: ["readWrite"]
})
```

#### 2.3 启动服务
```bash
# 开发模式
npm run dev

# 生产模式
npm start

# 使用PM2管理
npm install -g pm2
pm2 start src/app.js --name timo-api
```

#### 2.4 Docker部署
```bash
# 构建镜像
docker build -t timo-cloud-service .

# 使用Docker Compose
docker-compose up -d
```

### 3. 微信小程序部署

#### 3.1 开发环境
1. 下载并安装微信开发者工具
2. 导入项目目录 `4_wechat_miniprogram`
3. 配置AppID和服务器域名
4. 在开发者工具中预览和调试

#### 3.2 发布流程
1. 代码审查和测试
2. 在开发者工具中上传代码
3. 在微信公众平台提交审核
4. 审核通过后发布

## 配置说明

### 固件配置

#### 主体固件配置 (sdkconfig)
```ini
# WiFi配置
CONFIG_ESP_WIFI_SSID="Your_WiFi_SSID"
CONFIG_ESP_WIFI_PASSWORD="Your_WiFi_Password"

# 显示屏配置
CONFIG_LCD_WIDTH=240
CONFIG_LCD_HEIGHT=240
CONFIG_LCD_SPI_FREQ=40000000

# 音频配置
CONFIG_AUDIO_SAMPLE_RATE=16000
CONFIG_AUDIO_BITS_PER_SAMPLE=16

# 传感器配置
CONFIG_SENSOR_UPDATE_INTERVAL=60
```

#### 底座固件配置 (sdkconfig)
```ini
# 蓝牙配置
CONFIG_BT_ENABLED=y
CONFIG_BTDM_CTRL_MODE_BLE_ONLY=y

# LED配置
CONFIG_LED_COUNT=24
CONFIG_LED_GPIO=8
```

### 云端服务配置 (.env)
```bash
# 应用配置
NODE_ENV=production
PORT=3000

# 数据库配置
MONGODB_URI=********************************************
REDIS_URL=redis://localhost:6379

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# 语音服务配置
BAIDU_ASR_API_KEY=your-baidu-asr-api-key
BAIDU_TTS_API_KEY=your-baidu-tts-api-key

# 微信配置
WECHAT_APP_ID=your-wechat-app-id
WECHAT_APP_SECRET=your-wechat-app-secret
```

### 小程序配置 (app.js)
```javascript
App({
  globalData: {
    apiBase: 'https://api.timo.com',
    wsBase: 'wss://ws.timo.com',
    version: '1.0.0'
  }
})
```

## 测试验证

### 1. 固件功能测试
- [ ] WiFi连接测试
- [ ] 蓝牙配对测试
- [ ] 显示屏显示测试
- [ ] 触摸屏响应测试
- [ ] 音频播放测试
- [ ] 传感器数据读取测试
- [ ] 语音识别测试
- [ ] 设备间通信测试

### 2. 云端服务测试
- [ ] API接口测试
- [ ] 数据库连接测试
- [ ] WebSocket连接测试
- [ ] 用户认证测试
- [ ] 设备管理测试
- [ ] 数据同步测试

### 3. 小程序功能测试
- [ ] 登录功能测试
- [ ] 设备绑定测试
- [ ] 状态监控测试
- [ ] 远程控制测试
- [ ] 宠物管理测试
- [ ] 任务管理测试

### 4. 集成测试
- [ ] 端到端通信测试
- [ ] 数据一致性测试
- [ ] 实时同步测试
- [ ] 异常处理测试
- [ ] 性能压力测试

## 监控和维护

### 1. 日志管理
```bash
# 查看应用日志
pm2 logs timo-api

# 查看系统日志
tail -f /var/log/timo/app.log

# 日志轮转配置
logrotate /etc/logrotate.d/timo
```

### 2. 性能监控
- 使用Prometheus + Grafana监控系统性能
- 监控API响应时间和错误率
- 监控数据库连接和查询性能
- 监控设备在线状态和连接质量

### 3. 备份策略
```bash
# 数据库备份
mongodump --db timo --out /backup/mongodb/$(date +%Y%m%d)

# 配置文件备份
tar -czf /backup/config/timo-config-$(date +%Y%m%d).tar.gz /etc/timo/

# 自动备份脚本
crontab -e
0 2 * * * /scripts/backup.sh
```

## 故障排除

### 常见问题

#### 1. 固件烧录失败
- 检查USB连接和驱动
- 确认开发板型号和配置
- 检查ESP-IDF环境配置

#### 2. 设备连接失败
- 检查WiFi网络配置
- 确认防火墙设置
- 检查服务器域名解析

#### 3. 小程序无法登录
- 检查AppID配置
- 确认服务器域名白名单
- 检查SSL证书配置

#### 4. 数据同步异常
- 检查WebSocket连接状态
- 确认数据库连接
- 检查网络延迟和稳定性

## 版本更新

### 固件OTA更新
1. 编译新版本固件
2. 上传到OTA服务器
3. 通过云端推送更新通知
4. 设备自动下载并更新

### 服务端更新
1. 代码审查和测试
2. 使用蓝绿部署策略
3. 数据库迁移（如需要）
4. 监控更新后状态

### 小程序更新
1. 提交新版本代码
2. 微信平台审核
3. 审核通过后发布
4. 用户自动更新

## 安全考虑

### 1. 网络安全
- 使用HTTPS/WSS加密通信
- 实施API访问限制
- 定期更新SSL证书

### 2. 数据安全
- 敏感数据加密存储
- 定期备份和恢复测试
- 访问权限控制

### 3. 设备安全
- 固件签名验证
- 安全启动配置
- 定期安全更新

## 联系支持

- **技术支持**: <EMAIL>
- **文档更新**: <EMAIL>
- **问题反馈**: <EMAIL>
