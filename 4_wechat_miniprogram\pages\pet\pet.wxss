/* 虚拟宠物页面样式 */

.pet-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  color: #fff;
  font-size: 28rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 宠物信息卡片 */
.pet-info-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  position: relative;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.pet-avatar {
  position: relative;
  margin-right: 30rpx;
}

.pet-sprite {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  animation: float 3s ease-in-out infinite;
}

.pet-sprite.happy {
  animation: bounce 0.5s ease-in-out infinite alternate;
}

.pet-sprite.excited {
  animation: shake 0.3s ease-in-out infinite;
}

.pet-sprite.sleepy {
  animation: sway 2s ease-in-out infinite;
}

.pet-face {
  font-size: 60rpx;
}

.pet-level {
  position: absolute;
  bottom: -10rpx;
  right: -10rpx;
  background: #667eea;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
}

.pet-details {
  flex: 1;
}

.pet-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.pet-status {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.status-text {
  color: #667eea;
  font-size: 28rpx;
}

.age-text {
  color: #999;
  font-size: 24rpx;
}

/* 属性条 */
.attributes {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.attribute-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.attr-label {
  width: 60rpx;
  font-size: 24rpx;
  color: #666;
}

.attr-bar {
  flex: 1;
  height: 12rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
}

.attr-fill {
  height: 100%;
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.attr-value {
  width: 50rpx;
  text-align: right;
  font-size: 24rpx;
  color: #666;
}

.customize-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background: #667eea;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28rpx;
}

/* 交互区域 */
.interaction-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.interaction-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.interaction-btn {
  aspect-ratio: 1;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;
  transition: transform 0.2s ease;
}

.interaction-btn:active {
  transform: scale(0.95);
}

.interaction-btn.active {
  animation: pulse 1s ease-in-out infinite;
}

.btn-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.btn-text {
  font-size: 24rpx;
  font-weight: bold;
}

.interaction-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-dots {
  display: flex;
  gap: 8rpx;
}

.dot {
  width: 8rpx;
  height: 8rpx;
  background: white;
  border-radius: 50%;
  animation: dot-bounce 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

/* 录音界面 */
.recording-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.recording-modal {
  background: white;
  border-radius: 30rpx;
  padding: 60rpx;
  text-align: center;
  max-width: 500rpx;
}

.recording-animation {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  margin: 0 auto 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wave {
  position: absolute;
  border: 4rpx solid #667eea;
  border-radius: 50%;
  animation: wave-pulse 2s ease-out infinite;
}

.wave1 { width: 100rpx; height: 100rpx; }
.wave2 { width: 140rpx; height: 140rpx; animation-delay: 0.3s; }
.wave3 { width: 180rpx; height: 180rpx; animation-delay: 0.6s; }

.microphone {
  font-size: 60rpx;
  z-index: 1;
}

.recording-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 30rpx;
}

.stop-btn {
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 统计区域 */
.stats-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 40rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 5rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.history-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 20rpx;
  background: #667eea;
  color: white;
  border-radius: 15rpx;
  font-size: 28rpx;
}

/* 自定义面板 */
.customize-overlay,
.history-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.customize-modal,
.history-modal {
  background: white;
  border-radius: 30rpx;
  width: 90%;
  max-height: 80%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 28rpx;
}

.customize-content,
.history-content {
  padding: 40rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

.customize-section {
  margin-bottom: 40rpx;
}

.section-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.type-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15rpx;
}

.type-option {
  text-align: center;
  padding: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 15rpx;
  transition: all 0.2s ease;
}

.type-option.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.type-icon {
  display: block;
  font-size: 40rpx;
  margin-bottom: 10rpx;
}

.type-name {
  font-size: 24rpx;
  color: #666;
}

.color-options {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15rpx;
}

.color-option {
  aspect-ratio: 1;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20rpx;
  border: 3rpx solid transparent;
  transition: all 0.2s ease;
}

.color-option.selected {
  border-color: #333;
  transform: scale(1.1);
}

.name-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 15rpx;
  font-size: 28rpx;
}

/* 历史记录 */
.history-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.history-icon {
  width: 60rpx;
  height: 60rpx;
  background: #667eea;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24rpx;
}

.history-details {
  flex: 1;
}

.history-type {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.history-content {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.history-time {
  font-size: 20rpx;
  color: #999;
}

.history-emotion {
  font-size: 40rpx;
}

.empty-history {
  text-align: center;
  padding: 60rpx 20rpx;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #ccc;
}

/* 动画效果 */
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10rpx); }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-20rpx); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5rpx); }
  75% { transform: translateX(5rpx); }
}

@keyframes sway {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(5deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes dot-bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

@keyframes wave-pulse {
  0% { transform: scale(0); opacity: 1; }
  100% { transform: scale(1); opacity: 0; }
}
