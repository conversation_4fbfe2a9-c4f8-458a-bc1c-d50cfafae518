/**
 * 通知模型
 * <AUTHOR> Team
 * @version 1.0.0
 */

const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
  // 用户ID
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  // 设备ID（可选，设备相关通知）
  deviceId: {
    type: String,
    index: true
  },
  
  // 通知标题
  title: {
    type: String,
    required: true,
    maxlength: 200
  },
  
  // 通知内容
  content: {
    type: String,
    required: true,
    maxlength: 1000
  },
  
  // 通知类型
  type: {
    type: String,
    required: true,
    enum: [
      'info',           // 信息通知
      'warning',        // 警告通知
      'error',          // 错误通知
      'success',        // 成功通知
      'alarm',          // 闹钟通知
      'reminder',       // 提醒通知
      'system',         // 系统通知
      'device_online',  // 设备上线
      'device_offline', // 设备离线
      'device_error',   // 设备错误
      'sensor_alert',   // 传感器报警
      'firmware_update',// 固件更新
      'theme_update',   // 主题更新
      'task_reminder',  // 任务提醒
      'focus_complete', // 专注完成
      'sleep_reminder', // 睡眠提醒
      'weather_alert'   // 天气预警
    ],
    index: true
  },
  
  // 优先级
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal',
    index: true
  },
  
  // 通知状态
  status: {
    type: String,
    enum: ['unread', 'read', 'archived'],
    default: 'unread',
    index: true
  },
  
  // 阅读时间
  readAt: {
    type: Date
  },
  
  // 归档时间
  archivedAt: {
    type: Date
  },
  
  // 操作URL
  actionUrl: {
    type: String,
    maxlength: 500
  },
  
  // 操作数据
  actionData: {
    type: mongoose.Schema.Types.Mixed
  },
  
  // 通知图标
  icon: {
    type: String,
    maxlength: 200
  },
  
  // 通知图片
  image: {
    type: String,
    maxlength: 500
  },
  
  // 通知声音
  sound: {
    type: String,
    maxlength: 200
  },
  
  // 是否震动
  vibrate: {
    type: Boolean,
    default: false
  },
  
  // 通知渠道
  channels: [{
    type: String,
    enum: ['app', 'email', 'sms', 'wechat', 'push'],
    default: 'app'
  }],
  
  // 发送状态
  delivery: {
    app: {
      sent: { type: Boolean, default: false },
      sentAt: Date,
      error: String
    },
    email: {
      sent: { type: Boolean, default: false },
      sentAt: Date,
      error: String
    },
    sms: {
      sent: { type: Boolean, default: false },
      sentAt: Date,
      error: String
    },
    wechat: {
      sent: { type: Boolean, default: false },
      sentAt: Date,
      error: String
    },
    push: {
      sent: { type: Boolean, default: false },
      sentAt: Date,
      error: String
    }
  },
  
  // 过期时间
  expiresAt: {
    type: Date
  },
  
  // 重复规则
  repeat: {
    enabled: { type: Boolean, default: false },
    pattern: { type: String, enum: ['daily', 'weekly', 'monthly', 'custom'] },
    interval: Number, // 间隔（分钟）
    endDate: Date,
    count: Number // 重复次数
  },
  
  // 相关数据
  metadata: {
    source: String,      // 通知来源
    category: String,    // 通知分类
    tags: [String],      // 标签
    reference: String,   // 关联ID
    version: String      // 版本号
  },
  
  // 用户交互
  interactions: [{
    action: { type: String, enum: ['click', 'dismiss', 'snooze', 'archive'] },
    timestamp: { type: Date, default: Date.now },
    data: mongoose.Schema.Types.Mixed
  }],
  
  // 统计信息
  stats: {
    views: { type: Number, default: 0 },
    clicks: { type: Number, default: 0 },
    dismissals: { type: Number, default: 0 }
  }
}, {
  timestamps: true,
  collection: 'notifications'
});

// 索引
notificationSchema.index({ userId: 1, status: 1, createdAt: -1 });
notificationSchema.index({ deviceId: 1, type: 1 });
notificationSchema.index({ type: 1, priority: 1 });
notificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// 虚拟字段：是否已过期
notificationSchema.virtual('isExpired').get(function() {
  return this.expiresAt && this.expiresAt < new Date();
});

// 虚拟字段：通知年龄（分钟）
notificationSchema.virtual('ageInMinutes').get(function() {
  return Math.floor((Date.now() - this.createdAt.getTime()) / (1000 * 60));
});

// 虚拟字段：格式化创建时间
notificationSchema.virtual('formattedCreatedAt').get(function() {
  const now = new Date();
  const diff = now - this.createdAt;
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;
  
  return this.createdAt.toLocaleDateString();
});

// 静态方法：获取用户未读通知
notificationSchema.statics.getUnreadByUser = function(userId, limit = 50) {
  return this.find({
    userId,
    status: 'unread',
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ]
  })
  .sort({ priority: -1, createdAt: -1 })
  .limit(limit);
};

// 静态方法：获取设备相关通知
notificationSchema.statics.getByDevice = function(deviceId, limit = 20) {
  return this.find({ deviceId })
    .sort({ createdAt: -1 })
    .limit(limit);
};

// 静态方法：获取特定类型通知
notificationSchema.statics.getByType = function(userId, type, limit = 20) {
  return this.find({ userId, type })
    .sort({ createdAt: -1 })
    .limit(limit);
};

// 静态方法：清理过期通知
notificationSchema.statics.cleanupExpired = function() {
  return this.deleteMany({
    expiresAt: { $lt: new Date() }
  });
};

// 静态方法：获取通知统计
notificationSchema.statics.getStats = function(userId, days = 7) {
  const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
  
  return this.aggregate([
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: '$type',
        total: { $sum: 1 },
        unread: {
          $sum: {
            $cond: [{ $eq: ['$status', 'unread'] }, 1, 0]
          }
        },
        highPriority: {
          $sum: {
            $cond: [{ $in: ['$priority', ['high', 'urgent']] }, 1, 0]
          }
        }
      }
    }
  ]);
};

// 实例方法：标记为已读
notificationSchema.methods.markAsRead = function() {
  this.status = 'read';
  this.readAt = new Date();
  this.stats.views += 1;
  return this.save();
};

// 实例方法：归档通知
notificationSchema.methods.archive = function() {
  this.status = 'archived';
  this.archivedAt = new Date();
  return this.save();
};

// 实例方法：添加交互记录
notificationSchema.methods.addInteraction = function(action, data = null) {
  this.interactions.push({
    action,
    data,
    timestamp: new Date()
  });
  
  // 更新统计
  switch (action) {
    case 'click':
      this.stats.clicks += 1;
      break;
    case 'dismiss':
      this.stats.dismissals += 1;
      break;
  }
  
  return this.save();
};

// 实例方法：检查是否可以发送
notificationSchema.methods.canSend = function() {
  if (this.isExpired) return false;
  if (this.status === 'archived') return false;
  return true;
};

// 实例方法：更新发送状态
notificationSchema.methods.updateDeliveryStatus = function(channel, success, error = null) {
  if (this.delivery[channel]) {
    this.delivery[channel].sent = success;
    this.delivery[channel].sentAt = new Date();
    if (error) {
      this.delivery[channel].error = error;
    }
  }
  return this.save();
};

// 中间件：保存前处理
notificationSchema.pre('save', function(next) {
  // 如果是新通知且没有设置过期时间，根据类型设置默认过期时间
  if (this.isNew && !this.expiresAt) {
    const defaultExpiry = {
      'info': 7 * 24 * 60 * 60 * 1000,      // 7天
      'warning': 30 * 24 * 60 * 60 * 1000,  // 30天
      'error': 30 * 24 * 60 * 60 * 1000,    // 30天
      'alarm': 24 * 60 * 60 * 1000,         // 1天
      'reminder': 24 * 60 * 60 * 1000       // 1天
    };
    
    const expiry = defaultExpiry[this.type];
    if (expiry) {
      this.expiresAt = new Date(Date.now() + expiry);
    }
  }
  
  next();
});

module.exports = mongoose.model('Notification', notificationSchema);
