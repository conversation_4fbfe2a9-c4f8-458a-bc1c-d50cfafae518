/**
 * @file network_manager.h
 * @brief TIMO网络管理器头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef NETWORK_MANAGER_H
#define NETWORK_MANAGER_H

#include "esp_err.h"
#include "bluetooth_system.h"
#include "esp_wifi.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化网络管理器
 * @return esp_err_t
 */
esp_err_t network_manager_init(void);

/**
 * @brief 启动WiFi
 * @return esp_err_t
 */
esp_err_t network_manager_start_wifi(void);

/**
 * @brief 启动蓝牙
 * @return esp_err_t
 */
esp_err_t network_manager_start_bluetooth(void);

/**
 * @brief 停止网络管理器
 * @return esp_err_t
 */
esp_err_t network_manager_stop(void);

/**
 * @brief 检查WiFi是否已连接
 * @return true 已连接
 * @return false 未连接
 */
bool network_manager_is_wifi_connected(void);

/**
 * @brief 检查蓝牙是否已连接
 * @return true 已连接
 * @return false 未连接
 */
bool network_manager_is_bluetooth_connected(void);

/**
 * @brief 获取WiFi信号强度
 * @return int8_t RSSI值
 */
int8_t network_manager_get_wifi_rssi(void);

/**
 * @brief 连接到指定的底座设备
 * @param device_addr 设备地址
 * @return esp_err_t
 */
esp_err_t network_manager_connect_to_base(const esp_bd_addr_t device_addr);

/**
 * @brief 断开与底座设备的连接
 * @return esp_err_t
 */
esp_err_t network_manager_disconnect_from_base(void);

/**
 * @brief 获取扫描到的底座设备列表
 * @param devices 设备列表缓冲区
 * @param max_count 最大设备数量
 * @return uint8_t 实际设备数量
 */
uint8_t network_manager_get_base_devices(bt_device_info_t *devices, uint8_t max_count);

/**
 * @brief 重新扫描底座设备
 * @return esp_err_t
 */
esp_err_t network_manager_rescan_base_devices(void);

/**
 * @brief 获取蓝牙连接状态
 * @return bt_connection_state_t
 */
bt_connection_state_t network_manager_get_bluetooth_state(void);

/**
 * @brief 连接到WiFi网络
 * @param ssid WiFi网络名称
 * @param password WiFi密码
 * @return esp_err_t
 */
esp_err_t network_manager_connect_wifi(const char *ssid, const char *password);

/**
 * @brief 扫描WiFi网络
 * @param ap_records AP记录缓冲区
 * @param ap_count AP数量指针
 * @return esp_err_t
 */
esp_err_t network_manager_scan_wifi(wifi_ap_record_t *ap_records, uint16_t *ap_count);

/**
 * @brief 断开WiFi连接
 * @return esp_err_t
 */
esp_err_t network_manager_disconnect_wifi(void);

#ifdef __cplusplus
}
#endif

#endif /* NETWORK_MANAGER_H */
