/**
 * WebSocket管理工具
 */

const auth = require('./auth');

class WebSocket {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.reconnectTimer = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 3000;
    this.heartbeatTimer = null;
    this.heartbeatInterval = 30000;
    this.listeners = new Map();
    this.url = '';
  }

  /**
   * 连接WebSocket
   */
  connect(url = null) {
    if (this.isConnected) {
      console.log('WebSocket已连接');
      return;
    }

    // 检查是否有token
    const token = auth.getToken();
    if (!token) {
      console.log('未登录，无法连接WebSocket');
      return;
    }

    // 使用传入的URL或全局配置
    this.url = url || getApp().globalData.wsBase;
    if (!this.url) {
      console.error('WebSocket URL未配置');
      return;
    }

    console.log('连接WebSocket:', this.url);

    try {
      this.socket = wx.connectSocket({
        url: `${this.url}?token=${token}`,
        protocols: ['timo-protocol']
      });

      this.setupEventHandlers();
    } catch (error) {
      console.error('WebSocket连接失败:', error);
      this.scheduleReconnect();
    }
  }

  /**
   * 设置事件处理器
   */
  setupEventHandlers() {
    if (!this.socket) return;

    // 连接成功
    this.socket.onOpen(() => {
      console.log('WebSocket连接成功');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.startHeartbeat();
      this.emit('connected');
    });

    // 接收消息
    this.socket.onMessage((res) => {
      try {
        const data = JSON.parse(res.data);
        console.log('WebSocket收到消息:', data);
        this.handleMessage(data);
      } catch (error) {
        console.error('WebSocket消息解析失败:', error);
      }
    });

    // 连接关闭
    this.socket.onClose((res) => {
      console.log('WebSocket连接关闭:', res);
      this.isConnected = false;
      this.stopHeartbeat();
      this.emit('disconnected', res);
      
      // 如果不是主动关闭，尝试重连
      if (res.code !== 1000) {
        this.scheduleReconnect();
      }
    });

    // 连接错误
    this.socket.onError((error) => {
      console.error('WebSocket连接错误:', error);
      this.isConnected = false;
      this.emit('error', error);
      this.scheduleReconnect();
    });
  }

  /**
   * 处理接收到的消息
   */
  handleMessage(data) {
    const { type, payload } = data;

    switch (type) {
      case 'heartbeat':
        // 心跳响应
        break;
      
      case 'device:status':
        // 设备状态更新
        this.emit('deviceStatus', payload);
        break;
      
      case 'sensor:data':
        // 传感器数据更新
        this.emit('sensorData', payload);
        break;
      
      case 'pet:status':
        // 宠物状态更新
        this.emit('petStatus', payload);
        break;
      
      case 'task:reminder':
        // 任务提醒
        this.emit('taskReminder', payload);
        break;
      
      case 'alert:triggered':
        // 预警触发
        this.emit('alertTriggered', payload);
        break;
      
      case 'notification':
        // 通知消息
        this.emit('notification', payload);
        this.showNotification(payload);
        break;
      
      default:
        // 其他消息类型
        this.emit(type, payload);
        break;
    }
  }

  /**
   * 发送消息
   */
  send(data) {
    if (!this.isConnected || !this.socket) {
      console.warn('WebSocket未连接，无法发送消息');
      return false;
    }

    try {
      this.socket.send({
        data: JSON.stringify(data)
      });
      return true;
    } catch (error) {
      console.error('WebSocket发送消息失败:', error);
      return false;
    }
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.socket) {
      this.socket.close({
        code: 1000,
        reason: '主动断开'
      });
      this.socket = null;
    }
    
    this.isConnected = false;
    this.stopHeartbeat();
    this.stopReconnect();
  }

  /**
   * 开始心跳
   */
  startHeartbeat() {
    this.stopHeartbeat();
    
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.send({
          type: 'heartbeat',
          timestamp: Date.now()
        });
      }
    }, this.heartbeatInterval);
  }

  /**
   * 停止心跳
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * 安排重连
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('WebSocket重连次数已达上限');
      return;
    }

    this.stopReconnect();
    
    this.reconnectTimer = setTimeout(() => {
      this.reconnectAttempts++;
      console.log(`WebSocket重连尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
      this.connect();
    }, this.reconnectInterval);
  }

  /**
   * 停止重连
   */
  stopReconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * 添加事件监听器
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  /**
   * 移除事件监听器
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   */
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('WebSocket事件处理错误:', error);
        }
      });
    }
  }

  /**
   * 显示通知
   */
  showNotification(notification) {
    const { title, content, type = 'info' } = notification;
    
    // 根据类型显示不同的通知
    switch (type) {
      case 'success':
        wx.showToast({
          title: title || content,
          icon: 'success'
        });
        break;
      
      case 'warning':
      case 'error':
        wx.showToast({
          title: title || content,
          icon: 'none'
        });
        break;
      
      case 'alert':
        wx.showModal({
          title: title || '提醒',
          content,
          showCancel: false
        });
        break;
      
      default:
        wx.showToast({
          title: title || content,
          icon: 'none'
        });
        break;
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      url: this.url
    };
  }
}

// 创建WebSocket实例
const websocket = new WebSocket();

module.exports = websocket;
