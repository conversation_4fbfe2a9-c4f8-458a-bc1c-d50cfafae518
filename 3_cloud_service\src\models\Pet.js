/**
 * 虚拟宠物模型
 * <AUTHOR> Team
 * @version 1.0.0
 */

const mongoose = require('mongoose');

const petSchema = new mongoose.Schema({
  // 宠物基本信息
  name: {
    type: String,
    required: true,
    maxlength: 50,
    default: '小TIMO'
  },
  
  // 用户ID
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true, // 每个用户只能有一个宠物
    index: true
  },
  
  // 设备ID
  deviceId: {
    type: String,
    required: true,
    index: true
  },
  
  // 宠物类型
  type: {
    type: String,
    enum: ['cat', 'dog', 'rabbit', 'bird', 'dragon', 'custom'],
    default: 'cat'
  },
  
  // 宠物外观
  appearance: {
    // 主色调
    primaryColor: { type: String, default: '#FF6B6B' },
    // 次色调
    secondaryColor: { type: String, default: '#4ECDC4' },
    // 眼睛颜色
    eyeColor: { type: String, default: '#333333' },
    // 皮肤/毛发纹理
    texture: { type: String, default: 'smooth' },
    // 配饰
    accessories: [{
      type: { type: String, enum: ['hat', 'glasses', 'bow', 'collar'] },
      color: String,
      style: String
    }]
  },
  
  // 生命周期状态
  lifecycle: {
    // 当前阶段
    stage: {
      type: String,
      enum: ['egg', 'baby', 'child', 'teen', 'adult', 'elder'],
      default: 'baby'
    },
    // 年龄（天数）
    age: { type: Number, default: 0 },
    // 生日
    birthday: { type: Date, default: Date.now },
    // 经验值
    experience: { type: Number, default: 0 },
    // 等级
    level: { type: Number, default: 1 }
  },
  
  // 基础属性
  attributes: {
    // 健康值 (0-100)
    health: { type: Number, default: 100, min: 0, max: 100 },
    // 快乐值 (0-100)
    happiness: { type: Number, default: 80, min: 0, max: 100 },
    // 饥饿值 (0-100, 100为最饿)
    hunger: { type: Number, default: 20, min: 0, max: 100 },
    // 疲劳值 (0-100, 100为最累)
    fatigue: { type: Number, default: 10, min: 0, max: 100 },
    // 清洁度 (0-100)
    cleanliness: { type: Number, default: 90, min: 0, max: 100 },
    // 智力值 (0-100)
    intelligence: { type: Number, default: 50, min: 0, max: 100 }
  },
  
  // 情感系统
  emotions: {
    // 当前主要情感
    primary: {
      type: String,
      enum: ['happy', 'sad', 'excited', 'calm', 'angry', 'scared', 'curious', 'sleepy', 'playful'],
      default: 'happy'
    },
    // 情感强度 (0-100)
    intensity: { type: Number, default: 50, min: 0, max: 100 },
    // 情感历史
    history: [{
      emotion: String,
      intensity: Number,
      trigger: String, // 触发原因
      timestamp: { type: Date, default: Date.now }
    }],
    // 情感倾向（性格特征）
    personality: {
      // 外向性 (-100 to 100, 负数内向，正数外向)
      extroversion: { type: Number, default: 0, min: -100, max: 100 },
      // 友善性 (-100 to 100)
      agreeableness: { type: Number, default: 50, min: -100, max: 100 },
      // 责任心 (-100 to 100)
      conscientiousness: { type: Number, default: 30, min: -100, max: 100 },
      // 情绪稳定性 (-100 to 100)
      emotionalStability: { type: Number, default: 40, min: -100, max: 100 },
      // 开放性 (-100 to 100)
      openness: { type: Number, default: 60, min: -100, max: 100 }
    }
  },
  
  // 技能系统
  skills: {
    // 语言理解能力
    languageUnderstanding: { type: Number, default: 30, min: 0, max: 100 },
    // 情感识别能力
    emotionRecognition: { type: Number, default: 40, min: 0, max: 100 },
    // 记忆能力
    memory: { type: Number, default: 50, min: 0, max: 100 },
    // 学习能力
    learning: { type: Number, default: 45, min: 0, max: 100 },
    // 创造力
    creativity: { type: Number, default: 35, min: 0, max: 100 }
  },
  
  // 交互记录
  interactions: {
    // 总交互次数
    totalCount: { type: Number, default: 0 },
    // 今日交互次数
    todayCount: { type: Number, default: 0 },
    // 最后交互时间
    lastInteraction: { type: Date, default: Date.now },
    // 交互类型统计
    typeStats: {
      voice: { type: Number, default: 0 },
      touch: { type: Number, default: 0 },
      gesture: { type: Number, default: 0 },
      task: { type: Number, default: 0 },
      play: { type: Number, default: 0 }
    },
    // 最近交互历史
    recent: [{
      type: { type: String, enum: ['voice', 'touch', 'gesture', 'task', 'play', 'feed', 'clean'] },
      content: String,
      userEmotion: String, // 用户情感
      petResponse: String, // 宠物反应
      timestamp: { type: Date, default: Date.now },
      duration: Number // 交互时长（秒）
    }]
  },
  
  // 学习记忆
  memory: {
    // 用户偏好
    userPreferences: {
      favoriteActivities: [String],
      preferredInteractionTime: [String], // 时间段
      voiceTone: String, // 用户常用语调
      topics: [String] // 常聊话题
    },
    // 重要事件记忆
    importantEvents: [{
      event: String,
      description: String,
      emotion: String,
      timestamp: Date,
      importance: { type: Number, min: 1, max: 10 }
    }],
    // 学习到的词汇
    vocabulary: [{
      word: String,
      meaning: String,
      usage: Number, // 使用频率
      learnedAt: Date
    }]
  },
  
  // 日常状态
  dailyStatus: {
    // 今日活动
    todayActivities: [{
      activity: String,
      timestamp: Date,
      duration: Number,
      enjoyment: Number // 享受程度
    }],
    // 睡眠状态
    sleep: {
      isAsleep: { type: Boolean, default: false },
      sleepStartTime: Date,
      sleepDuration: Number, // 分钟
      sleepQuality: { type: Number, min: 0, max: 100 }
    },
    // 最后更新时间
    lastUpdate: { type: Date, default: Date.now }
  },
  
  // 成就系统
  achievements: [{
    id: String,
    name: String,
    description: String,
    unlockedAt: Date,
    category: { type: String, enum: ['interaction', 'growth', 'learning', 'special'] }
  }],
  
  // 宠物状态
  status: {
    // 当前状态
    current: {
      type: String,
      enum: ['idle', 'playing', 'sleeping', 'eating', 'learning', 'sick', 'excited'],
      default: 'idle'
    },
    // 位置（在屏幕上的位置）
    position: {
      x: { type: Number, default: 50 },
      y: { type: Number, default: 50 }
    },
    // 当前动画
    animation: { type: String, default: 'idle' },
    // 是否可见
    visible: { type: Boolean, default: true },
    // 大小（任务时缩小）
    size: { type: Number, default: 100, min: 20, max: 100 }
  },
  
  // 设置选项
  settings: {
    // 是否启用宠物
    enabled: { type: Boolean, default: true },
    // 交互频率
    interactionFrequency: { type: String, enum: ['low', 'medium', 'high'], default: 'medium' },
    // 自动成长
    autoGrowth: { type: Boolean, default: true },
    // 声音开关
    soundEnabled: { type: Boolean, default: true },
    // 动画开关
    animationEnabled: { type: Boolean, default: true }
  }
}, {
  timestamps: true,
  collection: 'pets'
});

// 索引
petSchema.index({ userId: 1 }, { unique: true });
petSchema.index({ deviceId: 1 });
petSchema.index({ 'lifecycle.level': 1 });
petSchema.index({ 'emotions.primary': 1 });

// 虚拟字段：宠物年龄（天数）
petSchema.virtual('ageInDays').get(function() {
  return Math.floor((Date.now() - this.lifecycle.birthday.getTime()) / (1000 * 60 * 60 * 24));
});

// 虚拟字段：整体健康状态
petSchema.virtual('overallHealth').get(function() {
  const { health, happiness, hunger, fatigue, cleanliness } = this.attributes;
  return Math.round((health + happiness + (100 - hunger) + (100 - fatigue) + cleanliness) / 5);
});

// 虚拟字段：情感描述
petSchema.virtual('emotionDescription').get(function() {
  const emotion = this.emotions.primary;
  const intensity = this.emotions.intensity;
  
  const intensityLevel = intensity > 80 ? '非常' : intensity > 60 ? '很' : intensity > 40 ? '有点' : '略微';
  
  const emotionMap = {
    happy: '开心',
    sad: '难过',
    excited: '兴奋',
    calm: '平静',
    angry: '生气',
    scared: '害怕',
    curious: '好奇',
    sleepy: '困倦',
    playful: '顽皮'
  };
  
  return `${intensityLevel}${emotionMap[emotion] || emotion}`;
});

// 静态方法：获取用户宠物
petSchema.statics.getUserPet = function(userId) {
  return this.findOne({ userId });
};

// 静态方法：创建新宠物
petSchema.statics.createPet = function(userId, deviceId, petData = {}) {
  const defaultPet = {
    userId,
    deviceId,
    name: petData.name || '小TIMO',
    type: petData.type || 'cat',
    lifecycle: {
      stage: 'baby',
      age: 0,
      birthday: new Date(),
      experience: 0,
      level: 1
    }
  };
  
  return this.create({ ...defaultPet, ...petData });
};

// 实例方法：更新情感状态
petSchema.methods.updateEmotion = function(emotion, intensity, trigger) {
  this.emotions.primary = emotion;
  this.emotions.intensity = Math.max(0, Math.min(100, intensity));
  
  // 记录情感历史
  this.emotions.history.push({
    emotion,
    intensity,
    trigger,
    timestamp: new Date()
  });
  
  // 保持历史记录数量限制
  if (this.emotions.history.length > 50) {
    this.emotions.history = this.emotions.history.slice(-50);
  }
  
  return this.save();
};

// 实例方法：增加经验值
petSchema.methods.addExperience = function(exp) {
  this.lifecycle.experience += exp;
  
  // 检查是否升级
  const newLevel = Math.floor(this.lifecycle.experience / 100) + 1;
  if (newLevel > this.lifecycle.level) {
    this.lifecycle.level = newLevel;
    this.updateEmotion('excited', 90, 'level_up');
  }
  
  return this.save();
};

// 实例方法：记录交互
petSchema.methods.recordInteraction = function(interactionData) {
  const { type, content, userEmotion, petResponse, duration } = interactionData;
  
  // 更新交互统计
  this.interactions.totalCount += 1;
  this.interactions.todayCount += 1;
  this.interactions.lastInteraction = new Date();
  this.interactions.typeStats[type] = (this.interactions.typeStats[type] || 0) + 1;
  
  // 记录交互历史
  this.interactions.recent.push({
    type,
    content,
    userEmotion,
    petResponse,
    duration,
    timestamp: new Date()
  });
  
  // 保持历史记录数量限制
  if (this.interactions.recent.length > 100) {
    this.interactions.recent = this.interactions.recent.slice(-100);
  }
  
  // 增加经验值
  this.addExperience(Math.floor(Math.random() * 5) + 1);
  
  return this.save();
};

// 实例方法：更新属性
petSchema.methods.updateAttributes = function(changes) {
  Object.keys(changes).forEach(key => {
    if (this.attributes[key] !== undefined) {
      this.attributes[key] = Math.max(0, Math.min(100, this.attributes[key] + changes[key]));
    }
  });
  
  return this.save();
};

// 实例方法：日常维护
petSchema.methods.dailyMaintenance = function() {
  // 增加年龄
  this.lifecycle.age += 1;
  
  // 自然属性变化
  this.updateAttributes({
    hunger: 10,      // 饥饿增加
    fatigue: 5,      // 疲劳增加
    cleanliness: -5, // 清洁度下降
    happiness: -2    // 快乐度略微下降
  });
  
  // 重置今日交互次数
  this.interactions.todayCount = 0;
  
  // 检查成长阶段
  this.checkGrowthStage();
  
  return this.save();
};

// 实例方法：检查成长阶段
petSchema.methods.checkGrowthStage = function() {
  const age = this.lifecycle.age;
  let newStage = this.lifecycle.stage;
  
  if (age >= 365) newStage = 'elder';
  else if (age >= 180) newStage = 'adult';
  else if (age >= 90) newStage = 'teen';
  else if (age >= 30) newStage = 'child';
  else if (age >= 7) newStage = 'baby';
  else newStage = 'egg';
  
  if (newStage !== this.lifecycle.stage) {
    this.lifecycle.stage = newStage;
    this.updateEmotion('excited', 95, 'growth_stage_change');
  }
};

// 中间件：保存前处理
petSchema.pre('save', function(next) {
  // 更新日常状态时间戳
  this.dailyStatus.lastUpdate = new Date();
  next();
});

module.exports = mongoose.model('Pet', petSchema);
