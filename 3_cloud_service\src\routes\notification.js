/**
 * 通知服务路由
 * <AUTHOR> Team
 * @version 1.0.0
 */

const express = require('express');
const router = express.Router();
const authMiddleware = require('../middleware/auth');
const logger = require('../utils/logger');

// 通知模型
const Notification = require('../models/Notification');

/**
 * 获取通知列表
 * GET /api/notification
 */
router.get('/', authMiddleware, async (req, res) => {
  try {
    const { 
      type, 
      status = 'all', 
      page = 1, 
      limit = 20 
    } = req.query;

    // 构建查询条件
    const query = { userId: req.user.id };
    if (type) query.type = type;
    if (status !== 'all') query.status = status;

    // 分页查询
    const skip = (page - 1) * limit;
    const notifications = await Notification.find(query)
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .skip(skip);

    const total = await Notification.countDocuments(query);
    const unreadCount = await Notification.countDocuments({
      userId: req.user.id,
      status: 'unread'
    });

    res.json({
      success: true,
      data: notifications,
      unreadCount,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    logger.error('Get notifications error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get notifications'
    });
  }
});

/**
 * 创建通知
 * POST /api/notification
 */
router.post('/', authMiddleware, async (req, res) => {
  try {
    const {
      title,
      content,
      type = 'info',
      priority = 'normal',
      deviceId,
      actionUrl,
      expiresAt
    } = req.body;

    if (!title || !content) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'Title and content are required'
      });
    }

    const notification = new Notification({
      userId: req.user.id,
      title,
      content,
      type,
      priority,
      deviceId,
      actionUrl,
      expiresAt: expiresAt ? new Date(expiresAt) : null
    });

    await notification.save();

    // 实时推送通知
    req.app.get('io').to(`user:${req.user.id}`).emit('notification:new', {
      id: notification._id,
      title: notification.title,
      content: notification.content,
      type: notification.type,
      priority: notification.priority,
      createdAt: notification.createdAt
    });

    res.json({
      success: true,
      message: 'Notification created successfully',
      data: notification
    });

  } catch (error) {
    logger.error('Create notification error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to create notification'
    });
  }
});

/**
 * 标记通知为已读
 * PUT /api/notification/:id/read
 */
router.put('/:id/read', authMiddleware, async (req, res) => {
  try {
    const notification = await Notification.findOneAndUpdate(
      { _id: req.params.id, userId: req.user.id },
      { status: 'read', readAt: new Date() },
      { new: true }
    );

    if (!notification) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Notification not found'
      });
    }

    res.json({
      success: true,
      message: 'Notification marked as read',
      data: notification
    });

  } catch (error) {
    logger.error('Mark notification as read error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to mark notification as read'
    });
  }
});

/**
 * 批量标记通知为已读
 * PUT /api/notification/read-all
 */
router.put('/read-all', authMiddleware, async (req, res) => {
  try {
    const { ids } = req.body;

    let query = { userId: req.user.id, status: 'unread' };
    if (ids && Array.isArray(ids)) {
      query._id = { $in: ids };
    }

    const result = await Notification.updateMany(
      query,
      { status: 'read', readAt: new Date() }
    );

    res.json({
      success: true,
      message: `${result.modifiedCount} notifications marked as read`,
      modifiedCount: result.modifiedCount
    });

  } catch (error) {
    logger.error('Mark all notifications as read error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to mark notifications as read'
    });
  }
});

/**
 * 删除通知
 * DELETE /api/notification/:id
 */
router.delete('/:id', authMiddleware, async (req, res) => {
  try {
    const notification = await Notification.findOneAndDelete({
      _id: req.params.id,
      userId: req.user.id
    });

    if (!notification) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Notification not found'
      });
    }

    res.json({
      success: true,
      message: 'Notification deleted successfully'
    });

  } catch (error) {
    logger.error('Delete notification error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to delete notification'
    });
  }
});

/**
 * 批量删除通知
 * DELETE /api/notification
 */
router.delete('/', authMiddleware, async (req, res) => {
  try {
    const { ids, type, olderThan } = req.body;

    let query = { userId: req.user.id };
    
    if (ids && Array.isArray(ids)) {
      query._id = { $in: ids };
    } else if (type) {
      query.type = type;
    } else if (olderThan) {
      query.createdAt = { $lt: new Date(olderThan) };
    }

    const result = await Notification.deleteMany(query);

    res.json({
      success: true,
      message: `${result.deletedCount} notifications deleted`,
      deletedCount: result.deletedCount
    });

  } catch (error) {
    logger.error('Batch delete notifications error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to delete notifications'
    });
  }
});

/**
 * 发送设备通知
 * POST /api/notification/device
 */
router.post('/device', authMiddleware, async (req, res) => {
  try {
    const {
      deviceId,
      type,
      title,
      content,
      priority = 'normal',
      actionData
    } = req.body;

    if (!deviceId || !type || !title || !content) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'deviceId, type, title, and content are required'
      });
    }

    // 创建通知记录
    const notification = new Notification({
      userId: req.user.id,
      deviceId,
      title,
      content,
      type: `device_${type}`,
      priority,
      actionData
    });

    await notification.save();

    // 发送到设备
    req.app.get('io').to(`device:${deviceId}`).emit('device:notification', {
      id: notification._id,
      type,
      title,
      content,
      priority,
      actionData,
      timestamp: new Date()
    });

    // 发送到用户
    req.app.get('io').to(`user:${req.user.id}`).emit('notification:new', {
      id: notification._id,
      title: notification.title,
      content: notification.content,
      type: notification.type,
      priority: notification.priority,
      createdAt: notification.createdAt
    });

    res.json({
      success: true,
      message: 'Device notification sent successfully',
      data: notification
    });

  } catch (error) {
    logger.error('Send device notification error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to send device notification'
    });
  }
});

/**
 * 获取通知统计
 * GET /api/notification/stats
 */
router.get('/stats', authMiddleware, async (req, res) => {
  try {
    const stats = await Notification.aggregate([
      { $match: { userId: req.user.id } },
      {
        $group: {
          _id: '$type',
          total: { $sum: 1 },
          unread: {
            $sum: {
              $cond: [{ $eq: ['$status', 'unread'] }, 1, 0]
            }
          }
        }
      }
    ]);

    const totalStats = await Notification.aggregate([
      { $match: { userId: req.user.id } },
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          unread: {
            $sum: {
              $cond: [{ $eq: ['$status', 'unread'] }, 1, 0]
            }
          },
          today: {
            $sum: {
              $cond: [
                {
                  $gte: [
                    '$createdAt',
                    new Date(new Date().setHours(0, 0, 0, 0))
                  ]
                },
                1,
                0
              ]
            }
          }
        }
      }
    ]);

    res.json({
      success: true,
      stats: {
        byType: stats,
        total: totalStats[0] || { total: 0, unread: 0, today: 0 }
      }
    });

  } catch (error) {
    logger.error('Get notification stats error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get notification statistics'
    });
  }
});

/**
 * 获取通知设置
 * GET /api/notification/settings
 */
router.get('/settings', authMiddleware, async (req, res) => {
  try {
    // TODO: 从用户设置中获取通知配置
    const settings = {
      email: {
        enabled: true,
        types: ['alarm', 'warning', 'system']
      },
      push: {
        enabled: true,
        types: ['alarm', 'warning', 'reminder']
      },
      sms: {
        enabled: false,
        types: ['alarm', 'warning']
      },
      wechat: {
        enabled: true,
        types: ['alarm', 'warning', 'reminder', 'system']
      }
    };

    res.json({
      success: true,
      settings
    });

  } catch (error) {
    logger.error('Get notification settings error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get notification settings'
    });
  }
});

/**
 * 更新通知设置
 * PUT /api/notification/settings
 */
router.put('/settings', authMiddleware, async (req, res) => {
  try {
    const { settings } = req.body;

    if (!settings) {
      return res.status(400).json({
        error: 'Missing settings',
        message: 'Settings object is required'
      });
    }

    // TODO: 保存用户通知设置到数据库
    
    res.json({
      success: true,
      message: 'Notification settings updated successfully',
      settings
    });

  } catch (error) {
    logger.error('Update notification settings error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to update notification settings'
    });
  }
});

module.exports = router;
