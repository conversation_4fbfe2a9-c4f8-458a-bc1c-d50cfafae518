{"timestamp": "2025-06-30T14:23:02.514026", "tests": {"project_structure": {"success": true, "missing_dirs": [], "missing_files": [], "message": "项目结构完整"}, "main_device_firmware": {"success": false, "message": "ESP-IDF环境未配置", "error": "'idf.py' 不是内部或外部命令，也不是可运行的程序\n或批处理文件。\n"}, "base_station_firmware": {"success": false, "message": "ESP-IDF环境未配置", "error": "'idf.py' 不是内部或外部命令，也不是可运行的程序\n或批处理文件。\n"}, "cloud_service": {"success": false, "dependencies_ok": false, "missing_files": [], "message": "云端服务配置有问题"}, "wechat_miniprogram": {"success": false, "pages_count": 27, "missing_pages": ["pages/device/device.wxml", "pages/data/data.wxml", "pages/settings/settings.js", "pages/settings/settings.wxml", "pages/alarm/alarm.js", "pages/alarm/alarm.wxml", "pages/light/light.js", "pages/light/light.wxml", "pages/environment/environment.js", "pages/environment/environment.wxml", "pages/sleep/sleep.js", "pages/sleep/sleep.wxml", "pages/config/config.js", "pages/config/config.wxml", "pages/about/about.js", "pages/about/about.wxml", "pages/device/bind.js", "pages/device/bind.wxml", "pages/device/detail.wxml", "pages/monitor/dashboard.js", "pages/monitor/dashboard.wxml", "pages/monitor/history.js", "pages/monitor/history.wxml", "pages/pet/list.js", "pages/pet/list.wxml", "pages/pet/detail.js", "pages/pet/detail.wxml", "pages/pet/create.js", "pages/pet/create.wxml", "pages/task/list.js", "pages/task/list.wxml", "pages/task/detail.js", "pages/task/detail.wxml", "pages/task/create.js", "pages/task/create.wxml", "pages/task/pomodoro.js", "pages/task/pomodoro.wxml", "pages/scene/list.js", "pages/scene/list.wxml", "pages/scene/control.js", "pages/scene/control.wxml", "pages/theme/store.js", "pages/theme/store.wxml", "pages/theme/preview.js", "pages/theme/preview.wxml", "pages/user/profile.js", "pages/user/profile.wxml"], "missing_utils": [], "message": "微信小程序配置有问题"}, "documentation": {"success": true, "missing_docs": [], "message": "文档完整"}}, "summary": {"total": 6, "passed": 2, "failed": 4, "skipped": 0}}