/**
 * 设备管理页面
 * <AUTHOR> Team
 * @version 1.0.0
 */

const app = getApp();
const api = require('../../utils/api');

Page({
  data: {
    // 设备列表
    devices: [],
    
    // 当前选中设备
    currentDevice: null,
    
    // 扫描状态
    scanning: false,
    scanDevices: [],
    
    // 页面状态
    loading: true,
    refreshing: false,
    
    // 显示模式
    showMode: 'list', // list, scan
    
    // 连接状态
    connecting: false
  },

  /**
   * 页面加载
   */
  onLoad() {
    console.log('设备页面加载');
    this.initPage();
  },

  /**
   * 页面显示
   */
  onShow() {
    console.log('设备页面显示');
    this.refreshDevices();
  },

  /**
   * 页面隐藏
   */
  onHide() {
    // 停止扫描
    if (this.data.scanning) {
      this.stopScan();
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshDevices(true);
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 检查登录状态
      if (!app.globalData.token) {
        wx.reLaunch({
          url: '/pages/login/login'
        });
        return;
      }

      // 设置当前设备
      this.setData({
        currentDevice: app.globalData.currentDevice
      });

      // 加载设备列表
      await this.loadDevices();
    } catch (error) {
      console.error('初始化设备页面失败:', error);
      app.showError('页面加载失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 刷新设备列表
   */
  async refreshDevices(isPullRefresh = false) {
    if (isPullRefresh) {
      this.setData({ refreshing: true });
    }

    try {
      await this.loadDevices();
    } catch (error) {
      console.error('刷新设备列表失败:', error);
      app.showError('刷新失败');
    } finally {
      if (isPullRefresh) {
        this.setData({ refreshing: false });
        wx.stopPullDownRefresh();
      }
    }
  },

  /**
   * 加载设备列表
   */
  async loadDevices() {
    try {
      const devices = await app.getDevices();
      
      this.setData({
        devices,
        currentDevice: app.globalData.currentDevice
      });
      
      console.log('设备列表:', devices);
    } catch (error) {
      console.error('加载设备列表失败:', error);
      throw error;
    }
  },

  /**
   * 设备项点击
   */
  onDeviceItemTap(e) {
    const { device } = e.currentTarget.dataset;
    
    wx.navigateTo({
      url: `/pages/device/detail?deviceId=${device.deviceId}`
    });
  },

  /**
   * 切换当前设备
   */
  async onSwitchDevice(e) {
    const { device } = e.currentTarget.dataset;
    
    try {
      app.setCurrentDevice(device);
      
      this.setData({
        currentDevice: device
      });
      
      app.showSuccess('设备切换成功');
    } catch (error) {
      console.error('切换设备失败:', error);
      app.showError('切换失败');
    }
  },

  /**
   * 添加设备
   */
  onAddDevice() {
    this.setData({
      showMode: 'scan',
      scanDevices: []
    });
    
    this.startScan();
  },

  /**
   * 开始扫描
   */
  startScan() {
    if (this.data.scanning) {
      return;
    }

    this.setData({
      scanning: true,
      scanDevices: []
    });

    // 检查蓝牙权限
    wx.authorize({
      scope: 'scope.bluetooth',
      success: () => {
        this.initBluetooth();
      },
      fail: () => {
        app.showError('需要蓝牙权限才能扫描设备');
        this.setData({ scanning: false });
      }
    });
  },

  /**
   * 初始化蓝牙
   */
  initBluetooth() {
    wx.openBluetoothAdapter({
      success: () => {
        console.log('蓝牙适配器初始化成功');
        this.startBluetoothDevicesDiscovery();
      },
      fail: (error) => {
        console.error('蓝牙适配器初始化失败:', error);
        app.showError('蓝牙初始化失败');
        this.setData({ scanning: false });
      }
    });
  },

  /**
   * 开始搜索蓝牙设备
   */
  startBluetoothDevicesDiscovery() {
    wx.startBluetoothDevicesDiscovery({
      services: [], // 可以指定TIMO设备的服务UUID
      success: () => {
        console.log('开始搜索蓝牙设备');
        this.onBluetoothDeviceFound();
        
        // 10秒后自动停止扫描
        setTimeout(() => {
          if (this.data.scanning) {
            this.stopScan();
          }
        }, 10000);
      },
      fail: (error) => {
        console.error('搜索蓝牙设备失败:', error);
        app.showError('搜索设备失败');
        this.setData({ scanning: false });
      }
    });
  },

  /**
   * 监听蓝牙设备发现
   */
  onBluetoothDeviceFound() {
    wx.onBluetoothDeviceFound((res) => {
      const devices = res.devices;
      const scanDevices = [...this.data.scanDevices];
      
      devices.forEach(device => {
        // 过滤TIMO设备（根据设备名称或服务UUID）
        if (device.name && device.name.includes('TIMO')) {
          // 检查是否已存在
          const exists = scanDevices.find(d => d.deviceId === device.deviceId);
          if (!exists) {
            scanDevices.push({
              deviceId: device.deviceId,
              name: device.name || '未知设备',
              rssi: device.RSSI,
              advertisData: device.advertisData
            });
          }
        }
      });
      
      this.setData({ scanDevices });
    });
  },

  /**
   * 停止扫描
   */
  stopScan() {
    if (!this.data.scanning) {
      return;
    }

    this.setData({ scanning: false });

    wx.stopBluetoothDevicesDiscovery({
      success: () => {
        console.log('停止搜索蓝牙设备');
      }
    });

    wx.closeBluetoothAdapter({
      success: () => {
        console.log('关闭蓝牙适配器');
      }
    });
  },

  /**
   * 连接扫描到的设备
   */
  async onConnectScanDevice(e) {
    const { device } = e.currentTarget.dataset;
    
    if (this.data.connecting) {
      return;
    }

    this.setData({ connecting: true });

    try {
      app.showLoading('连接设备中...');
      
      // 停止扫描
      this.stopScan();
      
      // 连接设备
      await this.connectDevice(device);
      
      // 绑定设备
      await this.bindDevice(device);
      
      // 刷新设备列表
      await this.loadDevices();
      
      // 返回列表模式
      this.setData({ showMode: 'list' });
      
      app.showSuccess('设备连接成功');
    } catch (error) {
      console.error('连接设备失败:', error);
      app.showError('连接失败');
    } finally {
      this.setData({ connecting: false });
      app.hideLoading();
    }
  },

  /**
   * 连接蓝牙设备
   */
  connectDevice(device) {
    return new Promise((resolve, reject) => {
      wx.createBLEConnection({
        deviceId: device.deviceId,
        success: () => {
          console.log('蓝牙连接成功');
          resolve();
        },
        fail: (error) => {
          console.error('蓝牙连接失败:', error);
          reject(error);
        }
      });
    });
  },

  /**
   * 绑定设备到账户
   */
  async bindDevice(device) {
    try {
      const deviceInfo = {
        deviceId: device.deviceId,
        name: device.name,
        type: 'main_device', // 或根据设备信息判断
        model: 'TIMO-001',
        hardwareVersion: '1.0.0',
        firmwareVersion: '1.0.0'
      };

      await api.bindDevice(deviceInfo);
      console.log('设备绑定成功');
    } catch (error) {
      console.error('设备绑定失败:', error);
      throw error;
    }
  },

  /**
   * 取消扫描
   */
  onCancelScan() {
    this.stopScan();
    this.setData({ showMode: 'list' });
  },

  /**
   * 删除设备
   */
  async onDeleteDevice(e) {
    const { device } = e.currentTarget.dataset;
    
    const result = await new Promise(resolve => {
      wx.showModal({
        title: '确认删除',
        content: `确定要删除设备"${device.name}"吗？`,
        success: resolve
      });
    });

    if (!result.confirm) {
      return;
    }

    try {
      app.showLoading('删除设备中...');
      
      await api.deleteDevice(device.deviceId);
      
      // 如果删除的是当前设备，清除当前设备
      if (this.data.currentDevice && this.data.currentDevice.deviceId === device.deviceId) {
        app.setCurrentDevice(null);
        this.setData({ currentDevice: null });
      }
      
      // 刷新设备列表
      await this.loadDevices();
      
      app.showSuccess('设备删除成功');
    } catch (error) {
      console.error('删除设备失败:', error);
      app.showError('删除失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 设备设置
   */
  onDeviceSettings(e) {
    const { device } = e.currentTarget.dataset;
    
    wx.navigateTo({
      url: `/pages/config/config?deviceId=${device.deviceId}`
    });
  }
});
