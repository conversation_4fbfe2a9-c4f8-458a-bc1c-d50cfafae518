/**
 * TIMO微信小程序入口文件
 * <AUTHOR> Team
 * @version 1.0.0
 */

const api = require('./utils/api');
const auth = require('./utils/auth');
const websocket = require('./utils/websocket');

App({
  // 全局数据
  globalData: {
    // API配置
    apiBase: 'https://api.timo.com',
    wsBase: 'wss://ws.timo.com',
    
    // 应用信息
    version: '1.0.0',
    appName: 'TIMO智能闹钟',
    
    // 用户信息
    userInfo: null,
    token: null,
    
    // 设备信息
    devices: [],
    currentDevice: null,
    
    // 系统信息
    systemInfo: null,
    
    // 网络状态
    networkType: 'unknown',
    isConnected: true,
    
    // WebSocket连接状态
    wsConnected: false
  },

  /**
   * 小程序初始化
   */
  onLaunch(options) {
    console.log('TIMO小程序启动', options);
    
    // 获取系统信息
    this.getSystemInfo();
    
    // 检查网络状态
    this.checkNetworkStatus();
    
    // 监听网络状态变化
    this.watchNetworkStatus();
    
    // 检查更新
    this.checkForUpdate();
    
    // 初始化认证
    this.initAuth();
    
    // 初始化API
    api.init(this.globalData.apiBase);
  },

  /**
   * 小程序显示
   */
  onShow(options) {
    console.log('TIMO小程序显示', options);
    
    // 重新检查网络状态
    this.checkNetworkStatus();
    
    // 如果已登录，刷新用户信息
    if (this.globalData.token) {
      this.refreshUserInfo();
    }
  },

  /**
   * 小程序隐藏
   */
  onHide() {
    console.log('TIMO小程序隐藏');
    
    // 断开WebSocket连接
    websocket.disconnect();
  },

  /**
   * 小程序错误处理
   */
  onError(error) {
    console.error('TIMO小程序错误:', error);
    
    // 错误上报
    this.reportError(error);
  },

  /**
   * 页面不存在处理
   */
  onPageNotFound(res) {
    console.warn('页面不存在:', res);
    
    // 重定向到首页
    wx.reLaunch({
      url: '/pages/index/index'
    });
  },

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      this.globalData.systemInfo = systemInfo;
      
      console.log('系统信息:', systemInfo);
    } catch (error) {
      console.error('获取系统信息失败:', error);
    }
  },

  /**
   * 检查网络状态
   */
  checkNetworkStatus() {
    wx.getNetworkType({
      success: (res) => {
        this.globalData.networkType = res.networkType;
        this.globalData.isConnected = res.networkType !== 'none';
        
        console.log('网络类型:', res.networkType);
        
        if (!this.globalData.isConnected) {
          this.showNetworkError();
        }
      },
      fail: (error) => {
        console.error('获取网络状态失败:', error);
      }
    });
  },

  /**
   * 监听网络状态变化
   */
  watchNetworkStatus() {
    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      this.globalData.isConnected = res.isConnected;
      this.globalData.networkType = res.networkType;
      
      console.log('网络状态变化:', res);
      
      if (res.isConnected) {
        // 网络恢复，重新连接WebSocket
        if (this.globalData.token) {
          websocket.connect();
        }
        
        // 隐藏网络错误提示
        wx.hideToast();
      } else {
        // 网络断开
        this.showNetworkError();
        websocket.disconnect();
      }
    });
  },

  /**
   * 显示网络错误
   */
  showNetworkError() {
    wx.showToast({
      title: '网络连接异常',
      icon: 'none',
      duration: 3000
    });
  },

  /**
   * 检查小程序更新
   */
  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      
      // 检查更新
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          console.log('发现新版本');
        }
      });
      
      // 下载更新
      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });
      
      // 更新失败
      updateManager.onUpdateFailed(() => {
        console.error('新版本下载失败');
      });
    }
  },

  /**
   * 初始化认证
   */
  initAuth() {
    // 从本地存储获取token
    const token = wx.getStorageSync('token');
    if (token) {
      this.globalData.token = token;
      auth.setToken(token);
      
      // 验证token有效性
      this.validateToken();
    }
  },

  /**
   * 验证token有效性
   */
  async validateToken() {
    try {
      const userInfo = await api.getUserInfo();
      this.globalData.userInfo = userInfo;
      
      // 连接WebSocket
      websocket.connect();
      
      console.log('用户信息:', userInfo);
    } catch (error) {
      console.error('Token验证失败:', error);
      
      // 清除无效token
      this.logout();
    }
  },

  /**
   * 刷新用户信息
   */
  async refreshUserInfo() {
    try {
      const userInfo = await api.getUserInfo();
      this.globalData.userInfo = userInfo;
    } catch (error) {
      console.error('刷新用户信息失败:', error);
    }
  },

  /**
   * 用户登录
   */
  async login(userInfo) {
    try {
      const result = await auth.login(userInfo);
      
      this.globalData.token = result.token;
      this.globalData.userInfo = result.user;
      
      // 保存到本地存储
      wx.setStorageSync('token', result.token);
      wx.setStorageSync('userInfo', result.user);
      
      // 连接WebSocket
      websocket.connect();
      
      console.log('登录成功:', result);
      
      return result;
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  },

  /**
   * 用户登出
   */
  logout() {
    // 清除全局数据
    this.globalData.token = null;
    this.globalData.userInfo = null;
    this.globalData.devices = [];
    this.globalData.currentDevice = null;
    
    // 清除本地存储
    wx.removeStorageSync('token');
    wx.removeStorageSync('userInfo');
    
    // 断开WebSocket
    websocket.disconnect();
    
    // 清除API token
    auth.clearToken();
    
    // 跳转到登录页
    wx.reLaunch({
      url: '/pages/login/login'
    });
    
    console.log('用户已登出');
  },

  /**
   * 获取设备列表
   */
  async getDevices() {
    try {
      const devices = await api.getDevices();
      this.globalData.devices = devices;
      
      // 如果没有当前设备，设置第一个为当前设备
      if (!this.globalData.currentDevice && devices.length > 0) {
        this.globalData.currentDevice = devices[0];
      }
      
      return devices;
    } catch (error) {
      console.error('获取设备列表失败:', error);
      throw error;
    }
  },

  /**
   * 设置当前设备
   */
  setCurrentDevice(device) {
    this.globalData.currentDevice = device;
    wx.setStorageSync('currentDevice', device);
    
    console.log('当前设备:', device);
  },

  /**
   * 错误上报
   */
  reportError(error) {
    // 这里可以集成错误监控服务
    console.error('错误上报:', error);
    
    // 可以发送到服务器
    // api.reportError(error);
  },

  /**
   * 显示加载提示
   */
  showLoading(title = '加载中...') {
    wx.showLoading({
      title,
      mask: true
    });
  },

  /**
   * 隐藏加载提示
   */
  hideLoading() {
    wx.hideLoading();
  },

  /**
   * 显示成功提示
   */
  showSuccess(title) {
    wx.showToast({
      title,
      icon: 'success',
      duration: 2000
    });
  },

  /**
   * 显示错误提示
   */
  showError(title) {
    wx.showToast({
      title,
      icon: 'none',
      duration: 3000
    });
  }
});
