/**
 * 主题服务路由
 * <AUTHOR> Team
 * @version 1.0.0
 */

const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const authMiddleware = require('../middleware/auth');
const logger = require('../utils/logger');

// 主题模型
const Theme = require('../models/Theme');

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/themes/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.zip', '.tar.gz'];
    const ext = path.extname(file.originalname).toLowerCase();
    
    if (allowedTypes.includes(ext) || file.originalname.endsWith('.tar.gz')) {
      cb(null, true);
    } else {
      cb(new Error('Only .zip and .tar.gz files are allowed'), false);
    }
  }
});

/**
 * 获取主题列表
 * GET /api/theme
 */
router.get('/', async (req, res) => {
  try {
    const { 
      category, 
      featured, 
      search, 
      sort = 'createdAt', 
      order = 'desc',
      page = 1, 
      limit = 20 
    } = req.query;

    // 构建查询条件
    const query = { status: 'published' };
    if (category) query.category = category;
    if (featured) query.featured = featured === 'true';
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // 排序选项
    const sortOptions = {};
    sortOptions[sort] = order === 'desc' ? -1 : 1;

    // 分页查询
    const skip = (page - 1) * limit;
    const themes = await Theme.find(query)
      .sort(sortOptions)
      .limit(parseInt(limit))
      .skip(skip)
      .populate('author', 'username avatar')
      .select('-filePath'); // 不返回文件路径

    const total = await Theme.countDocuments(query);

    res.json({
      success: true,
      data: themes,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    logger.error('Get themes error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get themes'
    });
  }
});

/**
 * 获取主题详情
 * GET /api/theme/:id
 */
router.get('/:id', async (req, res) => {
  try {
    const theme = await Theme.findById(req.params.id)
      .populate('author', 'username avatar')
      .populate('reviews.user', 'username avatar');

    if (!theme) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Theme not found'
      });
    }

    // 增加查看次数
    theme.views += 1;
    await theme.save();

    res.json({
      success: true,
      data: theme
    });

  } catch (error) {
    logger.error('Get theme detail error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get theme detail'
    });
  }
});

/**
 * 上传主题
 * POST /api/theme
 */
router.post('/', authMiddleware, upload.single('themeFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        error: 'Missing theme file',
        message: 'Theme file is required'
      });
    }

    const {
      name,
      description,
      category = 'custom',
      tags = [],
      version = '1.0.0',
      compatibility = ['1.0.0']
    } = req.body;

    if (!name || !description) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'Name and description are required'
      });
    }

    // 创建主题记录
    const theme = new Theme({
      name,
      description,
      category,
      tags: Array.isArray(tags) ? tags : tags.split(',').map(tag => tag.trim()),
      version,
      compatibility: Array.isArray(compatibility) ? compatibility : compatibility.split(',').map(v => v.trim()),
      author: req.user.id,
      filePath: req.file.path,
      fileName: req.file.originalname,
      fileSize: req.file.size,
      status: 'pending' // 需要审核
    });

    await theme.save();

    res.json({
      success: true,
      message: 'Theme uploaded successfully',
      data: {
        id: theme._id,
        name: theme.name,
        status: theme.status
      }
    });

  } catch (error) {
    logger.error('Upload theme error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to upload theme'
    });
  }
});

/**
 * 下载主题
 * GET /api/theme/:id/download
 */
router.get('/:id/download', authMiddleware, async (req, res) => {
  try {
    const theme = await Theme.findById(req.params.id);

    if (!theme) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Theme not found'
      });
    }

    if (theme.status !== 'published') {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Theme is not available for download'
      });
    }

    // 检查文件是否存在
    try {
      await fs.access(theme.filePath);
    } catch (error) {
      return res.status(404).json({
        error: 'File Not Found',
        message: 'Theme file not found on server'
      });
    }

    // 增加下载次数
    theme.downloads += 1;
    await theme.save();

    // 设置下载响应头
    res.setHeader('Content-Disposition', `attachment; filename="${theme.fileName}"`);
    res.setHeader('Content-Type', 'application/octet-stream');

    // 发送文件
    res.download(theme.filePath, theme.fileName);

  } catch (error) {
    logger.error('Download theme error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to download theme'
    });
  }
});

/**
 * 点赞主题
 * POST /api/theme/:id/like
 */
router.post('/:id/like', authMiddleware, async (req, res) => {
  try {
    const theme = await Theme.findById(req.params.id);

    if (!theme) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Theme not found'
      });
    }

    const userId = req.user.id;
    const likedIndex = theme.likes.indexOf(userId);

    if (likedIndex > -1) {
      // 取消点赞
      theme.likes.splice(likedIndex, 1);
    } else {
      // 添加点赞
      theme.likes.push(userId);
    }

    await theme.save();

    res.json({
      success: true,
      liked: likedIndex === -1,
      likesCount: theme.likes.length
    });

  } catch (error) {
    logger.error('Like theme error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to like theme'
    });
  }
});

/**
 * 收藏主题
 * POST /api/theme/:id/favorite
 */
router.post('/:id/favorite', authMiddleware, async (req, res) => {
  try {
    const theme = await Theme.findById(req.params.id);

    if (!theme) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Theme not found'
      });
    }

    const userId = req.user.id;
    const favoritedIndex = theme.favorites.indexOf(userId);

    if (favoritedIndex > -1) {
      // 取消收藏
      theme.favorites.splice(favoritedIndex, 1);
    } else {
      // 添加收藏
      theme.favorites.push(userId);
    }

    await theme.save();

    res.json({
      success: true,
      favorited: favoritedIndex === -1,
      favoritesCount: theme.favorites.length
    });

  } catch (error) {
    logger.error('Favorite theme error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to favorite theme'
    });
  }
});

/**
 * 评价主题
 * POST /api/theme/:id/review
 */
router.post('/:id/review', authMiddleware, async (req, res) => {
  try {
    const { rating, comment } = req.body;

    if (!rating || rating < 1 || rating > 5) {
      return res.status(400).json({
        error: 'Invalid rating',
        message: 'Rating must be between 1 and 5'
      });
    }

    const theme = await Theme.findById(req.params.id);

    if (!theme) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Theme not found'
      });
    }

    // 检查用户是否已经评价过
    const existingReview = theme.reviews.find(
      review => review.user.toString() === req.user.id
    );

    if (existingReview) {
      // 更新现有评价
      existingReview.rating = rating;
      existingReview.comment = comment;
      existingReview.updatedAt = new Date();
    } else {
      // 添加新评价
      theme.reviews.push({
        user: req.user.id,
        rating,
        comment,
        createdAt: new Date()
      });
    }

    // 重新计算平均评分
    const totalRating = theme.reviews.reduce((sum, review) => sum + review.rating, 0);
    theme.rating = totalRating / theme.reviews.length;

    await theme.save();

    res.json({
      success: true,
      message: 'Review submitted successfully',
      rating: theme.rating,
      reviewsCount: theme.reviews.length
    });

  } catch (error) {
    logger.error('Review theme error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to submit review'
    });
  }
});

/**
 * 获取主题分类
 * GET /api/theme/categories
 */
router.get('/meta/categories', async (req, res) => {
  try {
    const categories = [
      { id: 'classic', name: '经典', description: '经典风格主题' },
      { id: 'modern', name: '现代', description: '现代简约主题' },
      { id: 'cartoon', name: '卡通', description: '卡通可爱主题' },
      { id: 'nature', name: '自然', description: '自然风景主题' },
      { id: 'tech', name: '科技', description: '科技感主题' },
      { id: 'minimal', name: '极简', description: '极简风格主题' },
      { id: 'custom', name: '自定义', description: '用户自定义主题' }
    ];

    res.json({
      success: true,
      categories
    });

  } catch (error) {
    logger.error('Get categories error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get categories'
    });
  }
});

/**
 * 创建新主题
 * POST /api/theme/create
 */
router.post('/create', authMiddleware, async (req, res) => {
  try {
    const { name, description, category, config } = req.body;
    const userId = req.user.id;

    // 验证必填字段
    if (!name || !config) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Name and config are required'
      });
    }

    // 创建主题
    const theme = new Theme({
      name,
      description: description || '',
      category: category || 'custom',
      author: userId,
      config,
      status: 'draft',
      version: '1.0.0',
      tags: [],
      downloads: 0,
      rating: 0,
      reviews: []
    });

    await theme.save();

    logger.info(`Theme created: ${theme._id} by user ${userId}`);

    res.status(201).json({
      success: true,
      message: 'Theme created successfully',
      theme: {
        id: theme._id,
        name: theme.name,
        description: theme.description,
        category: theme.category,
        config: theme.config,
        status: theme.status,
        version: theme.version,
        createdAt: theme.createdAt
      }
    });

  } catch (error) {
    logger.error('Create theme error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to create theme'
    });
  }
});

/**
 * 更新主题
 * PUT /api/theme/:id
 */
router.put('/:id', authMiddleware, async (req, res) => {
  try {
    const themeId = req.params.id;
    const userId = req.user.id;
    const { name, description, category, config } = req.body;

    // 查找主题
    const theme = await Theme.findById(themeId);
    if (!theme) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Theme not found'
      });
    }

    // 检查权限
    if (theme.author.toString() !== userId) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'You can only edit your own themes'
      });
    }

    // 更新主题
    if (name) theme.name = name;
    if (description !== undefined) theme.description = description;
    if (category) theme.category = category;
    if (config) theme.config = config;

    theme.updatedAt = new Date();

    await theme.save();

    logger.info(`Theme updated: ${themeId} by user ${userId}`);

    res.json({
      success: true,
      message: 'Theme updated successfully',
      theme: {
        id: theme._id,
        name: theme.name,
        description: theme.description,
        category: theme.category,
        config: theme.config,
        status: theme.status,
        version: theme.version,
        updatedAt: theme.updatedAt
      }
    });

  } catch (error) {
    logger.error('Update theme error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to update theme'
    });
  }
});

/**
 * 发布主题
 * POST /api/theme/:id/publish
 */
router.post('/:id/publish', authMiddleware, async (req, res) => {
  try {
    const themeId = req.params.id;
    const userId = req.user.id;

    // 查找主题
    const theme = await Theme.findById(themeId);
    if (!theme) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Theme not found'
      });
    }

    // 检查权限
    if (theme.author.toString() !== userId) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'You can only publish your own themes'
      });
    }

    // 检查主题状态
    if (theme.status === 'published') {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Theme is already published'
      });
    }

    // 发布主题
    theme.status = 'published';
    theme.publishedAt = new Date();
    theme.updatedAt = new Date();

    await theme.save();

    logger.info(`Theme published: ${themeId} by user ${userId}`);

    res.json({
      success: true,
      message: 'Theme published successfully',
      theme: {
        id: theme._id,
        name: theme.name,
        status: theme.status,
        publishedAt: theme.publishedAt
      }
    });

  } catch (error) {
    logger.error('Publish theme error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to publish theme'
    });
  }
});

module.exports = router;
