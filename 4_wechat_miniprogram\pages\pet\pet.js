/**
 * 虚拟宠物页面
 * <AUTHOR> Team
 * @version 1.0.0
 */

const app = getApp();
const api = require('../../utils/api');

Page({
  data: {
    // 宠物信息
    pet: null,
    
    // 宠物状态
    petStatus: {
      basic: {},
      attributes: {},
      emotion: {},
      status: {},
      health: 0,
      interactions: {}
    },
    
    // 交互按钮
    interactionButtons: [
      { id: 'voice', name: '聊天', icon: 'chat', color: '#FF6B6B' },
      { id: 'touch', name: '抚摸', icon: 'touch', color: '#4ECDC4' },
      { id: 'play', name: '玩耍', icon: 'play', color: '#45B7D1' },
      { id: 'feed', name: '喂食', icon: 'food', color: '#96CEB4' },
      { id: 'clean', name: '清洁', icon: 'clean', color: '#FECA57' },
      { id: 'gesture', name: '手势', icon: 'gesture', color: '#FF9FF3' }
    ],
    
    // 宠物外观选项
    appearanceOptions: {
      types: [
        { value: 'cat', name: '小猫', icon: '🐱' },
        { value: 'dog', name: '小狗', icon: '🐶' },
        { value: 'rabbit', name: '兔子', icon: '🐰' },
        { value: 'bird', name: '小鸟', icon: '🐦' },
        { value: 'dragon', name: '小龙', icon: '🐲' }
      ],
      colors: [
        { name: '粉红', value: '#FF6B6B' },
        { name: '蓝绿', value: '#4ECDC4' },
        { name: '天蓝', value: '#45B7D1' },
        { name: '薄荷', value: '#96CEB4' },
        { name: '金黄', value: '#FECA57' },
        { name: '紫色', value: '#FF9FF3' }
      ]
    },
    
    // 页面状态
    loading: true,
    refreshing: false,
    showCustomize: false,
    showInteractionHistory: false,
    
    // 交互状态
    interacting: false,
    currentInteraction: null,
    
    // 语音交互
    recording: false,
    recordingTime: 0,
    recordingTimer: null
  },

  /**
   * 页面加载
   */
  onLoad() {
    console.log('宠物页面加载');
    this.initPage();
  },

  /**
   * 页面显示
   */
  onShow() {
    console.log('宠物页面显示');
    this.refreshPetData();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshPetData(true);
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 检查登录状态
      if (!app.globalData.token) {
        wx.reLaunch({
          url: '/pages/login/login'
        });
        return;
      }

      // 加载宠物数据
      await this.loadPetData();
    } catch (error) {
      console.error('初始化宠物页面失败:', error);
      app.showError('页面加载失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 刷新宠物数据
   */
  async refreshPetData(isPullRefresh = false) {
    if (isPullRefresh) {
      this.setData({ refreshing: true });
    }

    try {
      await this.loadPetData();
    } catch (error) {
      console.error('刷新宠物数据失败:', error);
      app.showError('刷新失败');
    } finally {
      if (isPullRefresh) {
        this.setData({ refreshing: false });
        wx.stopPullDownRefresh();
      }
    }
  },

  /**
   * 加载宠物数据
   */
  async loadPetData() {
    try {
      // 并行加载宠物信息和状态
      const [pet, status] = await Promise.all([
        this.loadPetInfo(),
        this.loadPetStatus()
      ]);

      console.log('宠物数据加载完成');
    } catch (error) {
      console.error('加载宠物数据失败:', error);
      throw error;
    }
  },

  /**
   * 加载宠物信息
   */
  async loadPetInfo() {
    try {
      const pet = await api.getPet();
      
      if (!pet) {
        // 如果没有宠物，显示创建界面
        this.showCreatePetDialog();
        return null;
      }

      this.setData({ pet });
      return pet;
    } catch (error) {
      console.error('加载宠物信息失败:', error);
      throw error;
    }
  },

  /**
   * 加载宠物状态
   */
  async loadPetStatus() {
    try {
      const status = await api.getPetStatus();
      
      this.setData({ petStatus: status });
      return status;
    } catch (error) {
      console.error('加载宠物状态失败:', error);
      // 不抛出错误，使用默认状态
    }
  },

  /**
   * 显示创建宠物对话框
   */
  showCreatePetDialog() {
    wx.showModal({
      title: '欢迎来到TIMO',
      content: '让我们一起创建你的专属虚拟宠物吧！',
      confirmText: '创建宠物',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          this.createPet();
        }
      }
    });
  },

  /**
   * 创建宠物
   */
  async createPet() {
    try {
      app.showLoading('正在创建宠物...');

      const deviceId = app.globalData.currentDevice?.deviceId;
      if (!deviceId) {
        app.showError('请先连接设备');
        return;
      }

      const petData = {
        deviceId,
        name: '小TIMO',
        type: 'cat'
      };

      const pet = await api.createPet(petData);
      
      this.setData({ pet });
      app.showSuccess('宠物创建成功！');
      
      // 重新加载数据
      await this.loadPetData();
    } catch (error) {
      console.error('创建宠物失败:', error);
      app.showError('创建失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 交互按钮点击
   */
  async onInteractionTap(e) {
    const { interaction } = e.currentTarget.dataset;
    
    if (this.data.interacting) {
      return;
    }

    this.setData({ 
      interacting: true,
      currentInteraction: interaction
    });

    try {
      switch (interaction) {
        case 'voice':
          await this.startVoiceInteraction();
          break;
        case 'touch':
          await this.startTouchInteraction();
          break;
        case 'play':
          await this.startPlayInteraction();
          break;
        case 'feed':
          await this.feedPet();
          break;
        case 'clean':
          await this.cleanPet();
          break;
        case 'gesture':
          await this.startGestureInteraction();
          break;
        default:
          console.log('未知交互类型:', interaction);
      }
    } catch (error) {
      console.error('交互失败:', error);
      app.showError('交互失败');
    } finally {
      this.setData({ 
        interacting: false,
        currentInteraction: null
      });
    }
  },

  /**
   * 开始语音交互
   */
  async startVoiceInteraction() {
    try {
      // 检查录音权限
      const authResult = await new Promise(resolve => {
        wx.authorize({
          scope: 'scope.record',
          success: () => resolve(true),
          fail: () => resolve(false)
        });
      });

      if (!authResult) {
        app.showError('需要录音权限才能进行语音交互');
        return;
      }

      // 开始录音
      this.startRecording();
    } catch (error) {
      console.error('语音交互失败:', error);
      app.showError('语音交互失败');
    }
  },

  /**
   * 开始录音
   */
  startRecording() {
    this.setData({ 
      recording: true,
      recordingTime: 0
    });

    // 开始录音
    wx.startRecord({
      success: (res) => {
        this.processVoiceInput(res.tempFilePath);
      },
      fail: (error) => {
        console.error('录音失败:', error);
        app.showError('录音失败');
        this.setData({ recording: false });
      }
    });

    // 录音计时器
    this.data.recordingTimer = setInterval(() => {
      this.setData({
        recordingTime: this.data.recordingTime + 1
      });

      // 最长录音60秒
      if (this.data.recordingTime >= 60) {
        this.stopRecording();
      }
    }, 1000);
  },

  /**
   * 停止录音
   */
  stopRecording() {
    if (!this.data.recording) {
      return;
    }

    this.setData({ recording: false });

    if (this.data.recordingTimer) {
      clearInterval(this.data.recordingTimer);
      this.data.recordingTimer = null;
    }

    wx.stopRecord();
  },

  /**
   * 处理语音输入
   */
  async processVoiceInput(audioPath) {
    try {
      app.showLoading('处理语音中...');

      // TODO: 上传音频文件并进行语音识别
      // 这里模拟语音识别结果
      const voiceContent = '你好，小TIMO！';

      const result = await api.petVoiceInteraction({
        content: voiceContent,
        userEmotion: 'happy',
        duration: this.data.recordingTime
      });

      this.handleInteractionResult(result);
    } catch (error) {
      console.error('处理语音输入失败:', error);
      app.showError('语音处理失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 开始触摸交互
   */
  async startTouchInteraction() {
    try {
      app.showLoading('抚摸中...');

      const result = await api.petTouchInteraction({
        position: { x: 50, y: 50 },
        pressure: 0.5,
        duration: 3
      });

      this.handleInteractionResult(result);
      app.showSuccess('宠物很享受你的抚摸~');
    } catch (error) {
      console.error('触摸交互失败:', error);
      app.showError('交互失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 开始游戏交互
   */
  async startPlayInteraction() {
    try {
      app.showLoading('和宠物玩耍中...');

      const result = await api.petPlay({
        gameType: 'ball',
        duration: 15
      });

      this.handleInteractionResult(result);
      app.showSuccess('玩得真开心！');
    } catch (error) {
      console.error('游戏交互失败:', error);
      app.showError('交互失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 喂食宠物
   */
  async feedPet() {
    try {
      app.showLoading('喂食中...');

      const result = await api.petFeed({
        foodType: 'default'
      });

      this.handleInteractionResult(result);
      app.showSuccess('宠物吃得很香~');
    } catch (error) {
      console.error('喂食失败:', error);
      app.showError('喂食失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 清洁宠物
   */
  async cleanPet() {
    try {
      app.showLoading('清洁中...');

      const result = await api.petClean();

      this.handleInteractionResult(result);
      app.showSuccess('宠物变得干净了~');
    } catch (error) {
      console.error('清洁失败:', error);
      app.showError('清洁失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 开始手势交互
   */
  async startGestureInteraction() {
    try {
      app.showLoading('识别手势中...');

      // TODO: 实现手势识别
      const result = await api.petGestureInteraction({
        gestureType: 'wave',
        gestureData: {}
      });

      this.handleInteractionResult(result);
      app.showSuccess('宠物学会了你的手势！');
    } catch (error) {
      console.error('手势交互失败:', error);
      app.showError('交互失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 处理交互结果
   */
  handleInteractionResult(result) {
    if (result && result.petResponse) {
      // 更新宠物状态
      if (result.attributes) {
        this.setData({
          'petStatus.attributes': result.attributes
        });
      }

      // 显示宠物反应
      if (result.petResponse.text) {
        wx.showToast({
          title: result.petResponse.text,
          icon: 'none',
          duration: 3000
        });
      }

      // 刷新宠物状态
      this.loadPetStatus();
    }
  },

  /**
   * 显示自定义界面
   */
  showCustomizePanel() {
    this.setData({ showCustomize: true });
  },

  /**
   * 隐藏自定义界面
   */
  hideCustomizePanel() {
    this.setData({ showCustomize: false });
  },

  /**
   * 更新宠物外观
   */
  async updatePetAppearance(e) {
    const { type, value } = e.currentTarget.dataset;
    
    try {
      app.showLoading('更新外观中...');

      const updates = {
        appearance: {
          ...this.data.pet.appearance,
          [type]: value
        }
      };

      const pet = await api.updatePet(updates);
      
      this.setData({ pet });
      app.showSuccess('外观更新成功');
    } catch (error) {
      console.error('更新外观失败:', error);
      app.showError('更新失败');
    } finally {
      app.hideLoading();
    }
  },

  /**
   * 显示交互历史
   */
  showInteractionHistory() {
    this.setData({ showInteractionHistory: true });
    this.loadInteractionHistory();
  },

  /**
   * 隐藏交互历史
   */
  hideInteractionHistory() {
    this.setData({ showInteractionHistory: false });
  },

  /**
   * 加载交互历史
   */
  async loadInteractionHistory() {
    try {
      const history = await api.getPetInteractions({ limit: 20 });
      
      this.setData({ interactionHistory: history });
    } catch (error) {
      console.error('加载交互历史失败:', error);
    }
  },

  /**
   * 获取属性颜色
   */
  getAttributeColor(value) {
    if (value >= 80) return '#4CAF50';
    if (value >= 60) return '#FF9800';
    if (value >= 40) return '#FFC107';
    return '#F44336';
  },

  /**
   * 获取情感图标
   */
  getEmotionIcon(emotion) {
    const icons = {
      happy: '😊',
      sad: '😢',
      excited: '🤩',
      calm: '😌',
      angry: '😠',
      scared: '😨',
      curious: '🤔',
      sleepy: '😴',
      playful: '😄'
    };
    return icons[emotion] || '😐';
  }
});
